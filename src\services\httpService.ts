import axios, { AxiosInstance } from 'axios';

// 配置API基础URL - 使用Nginx代理
const httpService: AxiosInstance = axios.create({
  baseURL: '/api',  // 使用相对路径，会被Nginx代理转发到后端
  // baseURL: 'http://127.0.0.1:4523/m1/6060246-5750403-default/', // 临时测试
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 300000  // 5分钟 (300000ms)
});

// 请求拦截器 - 添加token到请求头
httpService.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理401未授权错误
httpService.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response && error.response.status === 401) {
      // 清除token并跳转到登录页
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default httpService;
