{"name": "rag-frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "axios": "^1.8.4", "marked": "^15.0.8", "uuid": "^11.1.0", "vue": "^3.3.4", "vue-router": "^4.2.5", "vue3-json-viewer": "^2.3.0", "vuex": "^4.1.0"}, "devDependencies": {"@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vitejs/plugin-vue": "^5.0.3", "@vue/eslint-config-typescript": "^9.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.69.5", "typescript": "^5.2.2", "vite": "^5.0.10", "vite-plugin-vue-devtools": "^7.7.6"}}