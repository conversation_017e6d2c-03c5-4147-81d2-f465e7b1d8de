/**
 * 字体渲染工具函数
 * 用于解决SSE流式渲染中数字字体显示问题
 */

/**
 * 强制重新渲染指定元素的字体
 * @param element - 需要重新渲染字体的DOM元素
 */
export function forceRefreshFont(element: HTMLElement): void {
  if (!element) return;
  
  // 保存原始字体设置
  const originalFontFamily = element.style.fontFamily;
  const originalFontVariant = element.style.fontVariantNumeric;
  const originalFontFeature = element.style.fontFeatureSettings;
  
  // 临时改变字体设置来强制浏览器重新计算
  element.style.fontFamily = 'serif';
  element.style.fontVariantNumeric = 'normal';
  element.style.fontFeatureSettings = 'normal';
  
  // 强制重排
  element.offsetHeight;
  
  // 使用requestAnimationFrame确保DOM更新完成后再恢复字体
  requestAnimationFrame(() => {
    element.style.fontFamily = originalFontFamily || "'Minecraft', 'Consolas', 'Monaco', 'Courier New', monospace";
    element.style.fontVariantNumeric = originalFontVariant || 'normal';
    element.style.fontFeatureSettings = originalFontFeature || 'normal';
    
    // 再次强制重排确保字体正确应用
    element.offsetHeight;
  });
}

/**
 * 强制重新渲染页面中所有消息文本的字体
 * 主要用于SSE流式渲染完成后修复数字字体显示问题
 */
export function forceRefreshAllMessageFonts(): void {
  // 查找所有消息文本元素
  const messageElements = document.querySelectorAll('.message-text');
  
  messageElements.forEach(element => {
    if (element instanceof HTMLElement) {
      forceRefreshFont(element);
      
      // 同时处理子元素
      const childElements = element.querySelectorAll('*');
      childElements.forEach(child => {
        if (child instanceof HTMLElement) {
          forceRefreshFont(child);
        }
      });
    }
  });
}

/**
 * 延迟执行字体刷新
 * @param delay - 延迟时间（毫秒）
 */
export function delayedFontRefresh(delay: number = 100): void {
  setTimeout(() => {
    forceRefreshAllMessageFonts();
  }, delay);
}

/**
 * 监听DOM变化并自动刷新字体
 * @param targetElement - 要监听的目标元素
 * @param callback - 可选的回调函数
 */
export function observeFontRefresh(targetElement: HTMLElement, callback?: () => void): MutationObserver {
  const observer = new MutationObserver((mutations) => {
    let shouldRefresh = false;
    
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' || mutation.type === 'characterData') {
        shouldRefresh = true;
      }
    });
    
    if (shouldRefresh) {
      // 延迟一小段时间确保DOM更新完成
      setTimeout(() => {
        forceRefreshFont(targetElement);
        callback?.();
      }, 50);
    }
  });
  
  observer.observe(targetElement, {
    childList: true,
    subtree: true,
    characterData: true
  });
  
  return observer;
}
