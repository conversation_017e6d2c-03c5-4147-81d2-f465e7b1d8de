// 用于mock API响应，现在用不到了
import { AxiosResponse } from 'axios';

// 类型定义
export interface ApiResponse {
  answer: string;
}

export interface MockCategory {
  keywords: string[];
  answers: string[];
}

// 测试用的模拟回答（保留部分示例，便于测试）
export const mockMarkdownResponses: MockCategory[] = [
  {
    keywords: ['合成', '制作', '怎么做', '配方'],
    answers: [
      `# Minecraft 合成指南

## 基础工具合成
要制作**木镐**，你需要以下材料：
- 3个木板
- 2个木棍

### 合成表
将材料按以下方式放置在工作台上：
\`\`\`
[木板] [木板] [木板]
[空]   [木棍] [空]
[空]   [木棍] [空]
\`\`\`

## 高级合成
制作**附魔台**需要：
- 4块黑曜石
- 2颗钻石
- 1本书

![附魔台图片](https://minecraft.fandom.com/wiki/Enchanting_Table)

> 提示：使用钻石镐才能挖掘黑曜石！`
    ]
  },
  {
    keywords: ['怪物', '苦力怕', '僵尸', '骷髅', '敌对'],
    answers: [
      `# Minecraft 敌对生物指南

## 苦力怕 (Creeper)
![Creeper](https://minecraft.fandom.com/wiki/Creeper)

苦力怕是Minecraft中最具标志性的敌对生物。

### 特点
- 无声靠近玩家
- **爆炸**造成环境破坏
- 害怕猫（会远离猫）

### 掉落物
* 0-2 个火药
* 音乐唱片（当被骷髅杀死时）

> 小贴士：制作盾牌可以减少爆炸伤害`
    ]
  }
];

/**
 * 模拟API响应，用于没有后端的测试
 */
export function mockAPIResponse(message: string): Promise<AxiosResponse<ApiResponse>> {
  return new Promise((resolve) => {
    // 减少模拟网络延迟，便于测试 (1-3秒)
    const delay = Math.floor(Math.random() * 2000) + 1000;

    setTimeout(() => {
      const lowercaseMsg = message.toLowerCase();

      // 查找匹配的回答类别
      const matchedCategory = mockMarkdownResponses.find(category =>
        category.keywords.some(keyword => lowercaseMsg.includes(keyword))
      );

      if (matchedCategory) {
        // 从匹配的类别中返回一个随机Markdown回答
        const randomIndex = Math.floor(Math.random() * matchedCategory.answers.length);
        resolve({
          data: {
            answer: matchedCategory.answers[randomIndex]
          },
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {} as any
        });
      } else {
        // 如果没有关键词匹配，返回默认回答
        resolve({
          data: {
            answer: `# 我不太确定这个问题

很抱歉，我对此类问题了解有限。

## 你可以尝试问这些问题：
* 关于**合成配方**的问题
* 关于**怪物**的问题 

或者，你可以查看 [Minecraft Wiki](https://minecraft.fandom.com) 获取更多详细信息！`
          },
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {} as any
        });
      }
    }, delay);
  });
}
