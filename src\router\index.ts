import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import ChatView from '../views/ChatView.vue'
import LoginView from '../views/auth/LoginView.vue'
import RegisterView from '../views/auth/RegisterView.vue'
import AdminRegisterView from '../views/auth/AdminRegisterView.vue'
import AdminView from '../views/admin/AdminView.vue'
import store from '../store'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'chat',
    component: ChatView,
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'login',
    component: LoginView,
    meta: { guestOnly: true }
  },
  {
    path: '/register',
    name: 'register',
    component: RegisterView,
    meta: { guestOnly: true }
  },
  {
    path: '/adminRegister',
    name: 'adminRegister',
    component: AdminRegisterView,
    meta: { guestOnly: true }
  },
  {
    path: '/admin',
    name: 'admin',
    component: AdminView,
    meta: { requiresAuth: true, requiresAdmin: true }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 全局导航守卫 - 处理路由权限
router.beforeEach(async (to, from, next) => {
  // 验证是否需要登录
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  const guestOnly = to.matched.some(record => record.meta.guestOnly);
  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin);
  
  // 检查用户是否已登录
  const isAuthenticated = store.getters.isAuthenticated;
  const isAdmin = store.getters.isAdmin;
  
  // 如果是首次访问并且用户已登录 - 尝试获取用户资料
  if (isAuthenticated && !store.state.user) {
    try {
      await store.dispatch('fetchCurrentUser');
    } catch (error) {
      console.error('获取用户信息失败', error);
    }
  }

  // 根据登录状态和路由要求决定导航
  if (requiresAuth && !isAuthenticated) {
    // 需要登录但未登录 - 重定向到登录页
    next({ name: 'login' });
  } else if (guestOnly && isAuthenticated) {
    // 只允许访客但已登录 - 重定向到首页
    next({ name: 'chat' });
  } else if (requiresAdmin && !isAdmin) {
    // 需要管理员权限但不是管理员 - 重定向到首页
    next({ name: 'chat' });
  } else {
    // 其他情况正常导航
    next();
  }
});

export default router