<template>
  <div class="tree-node">    <div class="node-card-wrapper">
      <!-- if recipe exists, show grid; otherwise show base resource card -->
      <template v-if="hasRecipe">
        <CraftingRecipeCard :item="node" @select-item="handleSelect" />
      </template>
      <template v-else>
        <BaseResourceCard :node="node" @select-item="handleSelect" />
      </template>      <div class="expand-btn-space">
        <button
          v-if="canExpand"
          class="expand-btn workbench-btn"
          @click="toggleExpand"
          :title="expanded ? '收起' : '展开'"
          :class="{ 'expanded': expanded }"
        >
          <div class="workbench-grid">
            <div v-for="i in 9" :key="i" class="grid-cell"></div>
          </div>
          <div class="expand-arrow">
            {{ expanded ? '▼' : '▶' }}
          </div>
        </button>
      </div>
    </div>    <transition name="fade">
      <div v-if="expanded && canExpand" class="tree-children">
        <div class="tree-connector-system">
          <div class="main-trunk">
            <div class="energy-flow"></div>
          </div>          <div class="branch-container">
            <div class="junction-node"></div>
            
            <!-- 多个子节点：显示水平分配管道系统 -->
            <template v-if="node.children.length > 1">
              <!-- 水平分配管道系统 -->
              <div class="horizontal-distributor">
                <div 
                  class="horizontal-pipe"
                  :style="{ '--pipe-width': calculatePipeWidth(node.children.length) }"
                ></div>
                <div class="horizontal-energy"></div>
              </div>
              <!-- 分支连接点和垂直线 -->
              <div class="branch-connections">
                <div 
                  v-for="(child, idx) in node.children" 
                  :key="idx"
                  class="branch-connection"
                  :style="{ '--branch-index': idx, '--total-branches': node.children.length }"
                >
                  <div class="connection-point"></div>
                  <div class="vertical-line">
                    <div class="vertical-energy"></div>
                  </div>
                </div>
              </div>
            </template>
            
            <!-- 单个子节点：直接垂直连接 -->
            <template v-else>
              <div class="single-branch">
                <div class="single-vertical-line">
                  <div class="single-vertical-energy"></div>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="tree-children-list">
          <CraftingTreeNode
            v-for="(child, idx) in node.children"
            :key="child.itemId + idx"
            :node="child"
            @insert="$emit('insert', $event)"
          />
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { CraftingTreeNodeData } from '@/types';
import CraftingRecipeCard from './CraftingRecipeCard.vue';
import BaseResourceCard from './BaseResourceCard.vue';

const props = defineProps<{ node: CraftingTreeNodeData }>();
const emit = defineEmits(['insert']);

const expanded = ref(false);
// can expand if there are child nodes (sub-recipes)
const canExpand = computed(() => props.node.children && props.node.children.length > 0);
// hasRecipe: show recipe if any slot in recipe grid is non-null
const hasRecipe = computed(() => props.node.recipe.flat().some((cell: string|null) => cell !== null));
function toggleExpand() {
  expanded.value = !expanded.value;
}

// Copy getItemName and findNode logic from CraftingRecipeCard
function findNode(id: string, nodes: CraftingTreeNodeData[]): CraftingTreeNodeData | undefined {
  for (const node of nodes) {
    if (node.itemId === id) return node;
    const found = findNode(id, node.children);
    if (found) return found;
  }
}
function getItemName(id: string): string {
  if (props.node.itemId === id) return props.node.itemName;
  const node = findNode(id, props.node.children);
  return node ? node.itemName : id;
}

function handleSelect(id: string) {
  emit('insert', getItemName(id));
}

// 计算水平管道宽度
function calculatePipeWidth(childrenCount: number): string {
  if (childrenCount <= 1) return '60px';
  // 基于子节点数量和间距计算合适的管道宽度
  const baseWidth = Math.min(childrenCount * 120, 400); // 限制最大宽度
  return `${baseWidth}px`;
}
</script>

<style scoped>
.tree-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 8px;
}
.node-card-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}
.expand-btn-space {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.expand-btn.workbench-btn {
  position: relative;
  width: 32px;
  height: 32px;
  background: #2a2a2a;
  border: 2px solid #55ff55;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.expand-btn.workbench-btn:hover {
  background: #333;
  border-color: #66ff66;
  box-shadow: 0 0 8px #55ff5555;
  transform: scale(1.05);
}
.expand-btn.workbench-btn.expanded {
  background: #1a3a1a;
  border-color: #77ff77;
}
.workbench-grid {
  position: absolute;
  display: grid;
  grid-template-columns: repeat(3, 6px);
  grid-template-rows: repeat(3, 6px);
  gap: 1px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.6;
}
.grid-cell {
  width: 6px;
  height: 6px;
  background: #555;
  border-radius: 1px;
  transition: background 0.3s ease;
}
.expand-btn.workbench-btn:hover .grid-cell {
  background: #666;
}
.expand-btn.workbench-btn.expanded .grid-cell {
  background: #55ff55;
  box-shadow: 0 0 2px #55ff55;
}
.expand-arrow {
  position: absolute;
  bottom: 2px;
  right: 2px;
  font-size: 8px;
  color: #55ff55;
  line-height: 1;
  text-shadow: 0 0 2px #55ff55;
  transition: all 0.3s ease;
}
.expand-btn.workbench-btn.expanded .expand-arrow {
  color: #77ff77;
  text-shadow: 0 0 3px #77ff77;
}
.expand-btn:hover {
  background: #333;
}
.tree-connector-system {
  position: relative;
  width: 100%;
  height: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 8px;
  /* 向左偏移以对齐到卡片中心，而不是包装器中心 */
  /* 偏移量 = (展开按钮宽度 + 间隙) / 2 = (32px + 8px) / 2 = 20px */
  transform: translateX(-20px);
}
.main-trunk {
  width: 4px;
  height: 25px;
  background: linear-gradient(to bottom, #55ff55, #44dd44);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 4px #55ff5544;
}
.energy-flow {
  position: absolute;
  width: 100%;
  height: 12px;
  background: linear-gradient(to bottom, 
    transparent 0%, 
    #77ff77 20%, 
    #99ff99 50%, 
    #77ff77 80%, 
    transparent 100%);
  border-radius: 2px;
  animation: flow-down 2s linear infinite;
}
.branch-container {
  position: relative;
  width: 100%;
  height: 25px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}
.junction-node {
  width: 8px;
  height: 8px;
  background: #55ff55;
  border-radius: 50%;
  position: absolute;
  top: 0;
  box-shadow: 0 0 6px #55ff55;
  z-index: 3;
}
/* 水平分配管道 */
.horizontal-distributor {
  position: absolute;
  top: 4px;
  width: 100%;
  height: 4px;
  display: flex;
  justify-content: center;
  z-index: 1;
}
.horizontal-pipe {
  height: 4px;
  background: linear-gradient(to right, #44dd44, #55ff55, #44dd44);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 4px #44dd4444;
  /* 使用动态计算的宽度 */
  width: var(--pipe-width, 120px);
}
.horizontal-energy {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 30px;
  background: linear-gradient(to right, 
    transparent 0%, 
    #77ff77 20%, 
    #99ff99 50%, 
    #77ff77 80%, 
    transparent 100%);
  border-radius: 2px;
  animation: flow-horizontal 3s linear infinite;
}
/* 分支连接系统 */
.branch-connections {
  position: absolute;
  top: 4px;
  width: 100%;
  height: 21px;
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  z-index: 2;
}
.branch-connection {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.connection-point {
  width: 6px;
  height: 6px;
  background: #55ff55;
  border-radius: 50%;
  box-shadow: 0 0 4px #55ff55;
  z-index: 3;
  margin-bottom: 2px;
}
.vertical-line {
  width: 3px;
  height: 15px;
  background: linear-gradient(to bottom, #44dd44, #33bb33);
  border-radius: 1px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 3px #44dd4444;
}
.vertical-energy {
  position: absolute;
  width: 100%;
  height: 8px;
  background: linear-gradient(to bottom, 
    transparent 0%, 
    #66ff66 30%, 
    #88ff88 70%, 
    transparent 100%);
  border-radius: 1px;  animation: flow-down 2.5s linear infinite;
  animation-delay: calc(var(--branch-index) * 0.3s + 0.5s); /* 延迟等待水平能量到达 */
}
/* 单个子节点的简化连接 */
.single-branch {
  position: absolute;
  top: 4px;
  width: 100%;
  height: 21px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  z-index: 2;
}
.single-vertical-line {
  width: 4px;
  height: 21px;
  background: linear-gradient(to bottom, #44dd44, #33bb33);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 4px #44dd4444;
}
.single-vertical-energy {
  position: absolute;
  width: 100%;
  height: 12px;
  background: linear-gradient(to bottom, 
    transparent 0%, 
    #66ff66 30%, 
    #88ff88 70%, 
    transparent 100%);
  border-radius: 2px;
  animation: flow-down 2s linear infinite;
  animation-delay: 0.3s; /* 简单延迟，直接从主干流下 */
}
@keyframes flow-down {
  0% { 
    transform: translateY(-150%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% { 
    transform: translateY(350%);
    opacity: 0;
  }
}
@keyframes flow-horizontal {
  0% { 
    transform: translateX(-150%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% { 
    transform: translateX(calc(100% + 150%));
    opacity: 0;
  }
}
.tree-children {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}
.tree-children-list {
  display: flex;
  gap: 40px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
}
.fade-enter-active, .fade-leave-active {
  transition: all 0.4s ease;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
.fade-enter-to, .fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}
</style>
