# Stage 1: Build stage
FROM node:20-alpine as build-stage

# Set the working directory for the build stage
WORKDIR /app

# Set npm registry mirror for faster builds in some regions
RUN npm config set registry https://registry.npmmirror.com

# Copy package.json and package-lock.json separately for better cache management
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy all application files into the container
COPY . .

# Run the build command (uses Vite for this project)
RUN npm run build

# Stage 2: Production stage
FROM nginx:alpine as production-stage

# Copy the built files from the build stage to the nginx directory
COPY --from=build-stage /app/dist /usr/share/nginx/html

# Add our custom nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose the default nginx port
EXPOSE 80

# Start nginx in the foreground
CMD ["nginx", "-g", "daemon off;"]