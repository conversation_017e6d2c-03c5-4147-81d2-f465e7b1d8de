// 分屏比例持久化服务（仅前端本地存储）

/**
 * 保存分屏比例到 localStorage
 * @param key 唯一标识
 * @param ratio 左侧宽度
 */
export function saveSplitPaneRatio(key: string, ratio: number): Promise<{ success: boolean }> {
  localStorage.setItem('splitpane-' + key, String(ratio));
  return Promise.resolve({ success: true });
}

/**
 * 从 localStorage 获取分屏比例
 * @param key 唯一标识
 */
export function getSplitPaneRatio(key: string): Promise<{ ratio: number }> {
  const v = localStorage.getItem('splitpane-' + key);
  return Promise.resolve({ ratio: v ? Number(v) : 400 });
}
