<template>
  <div v-if="isVisible" class="feedback-overlay" @click.self="closeModal">
    <div class="feedback-modal">
      <div class="feedback-header">
        <h2>提交反馈</h2>
        <button @click="closeModal" class="close-button">&times;</button>
      </div>
      
      <form @submit.prevent="submitFeedback" class="feedback-form">
        <div class="alert success" v-if="successMessage">{{ successMessage }}</div>
        <div class="alert error" v-if="error">{{ error }}</div>
        
        <div class="form-group">
          <label for="feedback-type">反馈类型</label>
          <select 
            id="feedback-type" 
            v-model="feedbackType" 
            required
          >
            <option value="" disabled selected>请选择反馈类型</option>
            <option value="bug">Bug报告</option>
            <option value="feature">功能建议</option>
            <option value="content">内容纠错</option>
            <option value="other">其他</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="feedback-content">反馈内容</label>
          <textarea 
            id="feedback-content" 
            v-model="feedbackContent" 
            placeholder="请详细描述您的反馈..." 
            required 
            rows="5"
          ></textarea>
        </div>
        
        <div class="form-actions">
          <button 
            type="submit" 
            class="submit-button" 
            :disabled="isSubmitting"
          >
            {{ isSubmitting ? '提交中...' : '提交反馈' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useStore } from 'vuex';

defineProps({
  isVisible: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits(['close']);

const store = useStore();
const feedbackType = ref('');
const feedbackContent = ref('');
const isSubmitting = ref(false);
const error = ref('');
const successMessage = ref('');

// 提交反馈
const submitFeedback = async () => {
  if (!feedbackType.value || !feedbackContent.value.trim()) {
    error.value = '请填写所有必填项';
    return;
  }
  try {
    isSubmitting.value = true;
    error.value = '';
    const success = await store.dispatch('submitFeedback', {
      type: feedbackType.value,
      content: feedbackContent.value.trim()
    });
    if (success) {
      successMessage.value = '感谢您的反馈！我们会认真阅读每一条意见。';
      // 重置表单
      feedbackType.value = '';
      feedbackContent.value = '';
      // 3秒后自动关闭
      setTimeout(() => {
        closeModal();
      }, 3000);
    }
  } catch (err: any) {
    error.value = err.message || '提交失败，请稍后再试';
  } finally {
    isSubmitting.value = false;
  }
};

// 关闭模态框
const closeModal = () => {
  error.value = '';
  successMessage.value = '';
  emit('close');
};
</script>

<style lang="scss" scoped>
.feedback-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s;
}

.feedback-modal {
  width: 90%;
  max-width: 500px;
  background-color: rgba(0, 0, 0, 0.9);
  border: 2px solid #5e5e5e;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.8);
  animation: slideDown 0.4s;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    color: #55ff55;
    margin: 0;
    font-family: 'Minecraft', monospace;
    text-shadow: 2px 2px #000;
  }
  
  .close-button {
    background: none;
    border: none;
    color: #aaaaaa;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      color: #ffffff;
    }
  }
}

.feedback-form {
  .form-group {
    margin-bottom: 16px;
    
    label {
      display: block;
      margin-bottom: 8px;
      color: #aaaaaa;
      font-size: 14px;
    }
    
    select, textarea {
      width: 100%;
      padding: 10px;
      background-color: rgba(0, 0, 0, 0.8); /* 更深的黑色背景 */
      border: 2px solid #5e5e5e;
      border-radius: 4px;
      color: #ffffff; /* 白色文字 */
      font-family: inherit;
      font-size: 14px;
      
      &:focus {
        outline: none;
        border-color: #55ff55;
        box-shadow: 0 0 0 2px rgba(85, 255, 85, 0.3);
      }
    }
    
    /* 下拉框选项样式 */
    select {
      appearance: none; /* 移除默认样式 */
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="%2355ff55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>');
      background-repeat: no-repeat;
      background-position: right 10px center;
      padding-right: 30px; /* 为下拉箭头留出空间 */
      
      /* 选项样式 */
      option {
        background-color: #000000;
        color: #ffffff;
        padding: 10px;
      }
    }
    
    textarea {
      resize: vertical;
      min-height: 100px;
      
      &::placeholder {
        color: #777777;
      }
    }
  }
  
  .form-actions {
    margin-top: 20px;
    
    .submit-button {
      width: 100%;
      height: 40px;
      background-color: #55ff55;
      border: none;
      border-radius: 4px;
      color: #000000;
      font-weight: bold;
      cursor: pointer;
      font-family: 'Minecraft', sans-serif;
      font-size: 16px;
      transition: all 0.2s;
      
      &:hover {
        background-color: #7dff7d;
        transform: translateY(-2px);
      }
      
      &:disabled {
        background-color: #777777;
        cursor: not-allowed;
        transform: none;
      }
    }
  }
}

.alert {
  padding: 10px;
  margin-bottom: 16px;
  border-radius: 4px;
  
  &.error {
    background-color: rgba(255, 73, 73, 0.2);
    border: 1px solid #ff4949;
    color: #ff7070;
  }
  
  &.success {
    background-color: rgba(73, 255, 73, 0.2);
    border: 1px solid #49ff49;
    color: #70ff70;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 480px) {
  .feedback-modal {
    width: 95%;
    padding: 15px;
  }
}
</style>