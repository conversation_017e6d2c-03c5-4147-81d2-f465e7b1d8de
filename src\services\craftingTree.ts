import creeperIcon from '@/assets/creeper.webp';

// mock数据
const mockTree = {
  itemId: 'diamond_sword',
  itemName: '钻石剑',
  itemIcon: creeperIcon,
  itemDesc: '钻石剑是Minecraft中最强的近战武器之一。',
  url: 'https://minecraft.fandom.com/zh/wiki/钻石剑',
  recipe: [
    [null, 'diamond', null],
    [null, 'diamond', null],
    [null, 'stick', null],
  ],
  children: [
    {
      itemId: 'diamond',
      itemName: '钻石',
      itemIcon: creeperIcon,
      itemDesc: '钻石，可用于合成多种高级物品。',
      url: 'https://minecraft.fandom.com/zh/wiki/钻石',
      recipe: [
        [null, null, null],
        [null, 'diamond_ore', null],
        [null, null, null],
      ],
      children: [
        {
          itemId: 'diamond_ore',
          itemName: '钻石矿石',
          itemIcon: creeperIcon,
          itemDesc: 'Y坐标16及以下矿洞中，用铁镐或更高级矿镐采集获得。',
          url: 'https://minecraft.fandom.com/zh/wiki/钻石矿石',
          recipe: [
            [null, null, null],
            [null, null, null],
            [null, null, null],
          ],
          children: []
        }
      ]
    },
    {
      itemId: 'stick',
      itemName: '木棒',
      itemIcon: creeperIcon,
      itemDesc: '木棒，基础合成材料。',
      url: 'https://minecraft.fandom.com/zh/wiki/木棒',
      recipe: [
        [null, null, null],
        [null, 'planks', null],
        [null, 'planks', null],
      ],
      children: [
        {
          itemId: 'planks',
          itemName: '木板',
          itemIcon: creeperIcon,
          itemDesc: '木板，由原木合成。',
          url: 'https://minecraft.fandom.com/zh/wiki/木板',
          recipe: [
            [null, null, null],
            [null, 'log', null],
            [null, null, null],
          ],
          children: [
            {
              itemId: 'log',
              itemName: '原木',
              itemIcon: creeperIcon,
              itemDesc: '原木可通过砍伐树木获得，是最基础的采集材料。',
              url: 'https://minecraft.fandom.com/zh/wiki/原木',
              recipe: [
                [null, null, null],
                [null, null, null],
                [null, null, null],
              ],
              children: []
            }
          ]
        }
      ]
    }
  ]
};

// 导出 mockTree 以供 mockCraftingMessage 使用
export { mockTree };
