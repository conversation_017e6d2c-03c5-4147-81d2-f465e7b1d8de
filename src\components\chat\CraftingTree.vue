<template>
  <div class="crafting-tree">
    <div class="tree-header">
      <button class="close-btn" @click="$emit('close')" title="收起分屏">✕</button>
      <img class="item-icon" :src="treeData?.itemIcon" alt="icon" v-if="treeData" />
      <div class="item-title">{{ treeData?.itemName || '合成树' }}</div>
      <div class="item-desc" v-if="treeData">{{ itemDesc }}</div>
    </div>
    <div class="tree-scroll-area">
      <div v-if="treeData" class="tree-root">
        <CraftingTreeNode
          :node="treeData"
          @insert="handleInsert"
        />
      </div>
      <div v-else class="tree-error">未找到合成树数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { CraftingTreeNodeData } from '@/types';
import CraftingTreeNode from './CraftingTreeNode.vue';

// 接收父组件传入的完整合成树数据
const props = defineProps<{ initialTree: CraftingTreeNodeData | null }>();
const emit = defineEmits(['close', 'insert']);

// 直接使用消息里的完整树
const treeData = computed(() => props.initialTree);

// 根节点描述
const itemDesc = computed(() => treeData.value?.itemDesc || '');

// 插入事件
function handleInsert(item: CraftingTreeNodeData) {
  emit('insert', item);
}
</script>

<style scoped>
.crafting-tree {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgba(30, 30, 30, 0.95);
  border-left: 2px solid #55ff55;
  min-width: 380px;
}
.tree-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 18px 24px 8px 24px; /* 恢复原padding，保证内容整体对齐 */
  border-bottom: 1px solid #333;
  position: relative;
  justify-content: flex-start;
}
.close-btn {
  position: static;
  margin-right: 12px;
  background: none;
  border: none;
  color: #bbb;
  font-size: 22px;
  cursor: pointer;
  transition: color 0.2s;
  align-self: center;
}
.close-btn:hover {
  color: #ff5555;
}
.item-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: #222;
}
.item-title {
  font-size: 22px;
  color: #55ff55;
  font-family: 'Minecraft', monospace;
  font-weight: bold;
}
.item-desc {
  color: #bbb;
  font-size: 14px;
  margin-left: 12px;
}
.tree-scroll-area {
  flex: 1;
  overflow-y: auto;
  padding: 24px 16px 16px 16px;
}
.tree-loading, .tree-error {
  color: #bbb;
  text-align: center;
  margin-top: 40px;
}
.tree-root {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
