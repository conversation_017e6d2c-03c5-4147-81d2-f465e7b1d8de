<template>
  <router-view />
</template>

<style lang="scss">
@font-face {
  font-family: 'Minecraft';
  src: url('@/assets/minecraft-font.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  unicode-range: U+0020-007F, U+0030-0039; /* 基本拉丁字符，包括英文字母、数字和基本符号，特别强调数字0-9 */
  font-display: swap; /* 优化字体加载 */
}

@font-face {
  font-family: 'Minecraft';
  src: url('@/assets/minecraft-font-cn.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  unicode-range: U+4E00-9FFF, U+3400-4DBF, U+20000-2A6DF, U+2A700-2B73F, U+2B740-2B81F, U+2B820-2CEAF, U+F900-FAFF, U+2F800-2FA1F; /* 完整的中文字符范围 */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden; /* 阻止页面滚动 */
}

body {
  font-family: 'Minecraft', 'Consolas', 'Monaco', 'Courier New', sans-serif;
  -webkit-font-smoothing: none;
  -moz-osx-font-smoothing: grayscale;
  color: #ffffff;
  background-color: #111111;
  line-height: 1.6;
}

#app {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

button, input, textarea {
  font-family: 'Minecraft', 'Consolas', 'Monaco', 'Courier New', sans-serif;
  -webkit-font-smoothing: none;
}

/* 调试：测试数字显示 */
.debug-numbers {
  font-family: 'Minecraft', 'Consolas', 'Monaco', 'Courier New', monospace;
  color: #ff0000;
  font-size: 20px;
  background: #000;
  padding: 10px;
  margin: 10px;
}

.debug-numbers::after {
  content: "测试数字: 0123456789";
}
</style>