# Minecraft物品合成导航功能实现规划

## 功能概述

为RAG应用新增Minecraft物品合成导航功能，实现类似ChatGPT Canvas的分屏界面，左侧对话右侧合成树可视化。

## 技术架构

- **前端**: Vue 3 + TypeScript + Vuex + SCSS
- **设计风格**: 继承现有Minecraft主题
- **布局模式**: 分屏布局（左40%对话，右60%合成树）
- **数据源**: 需要Minecraft合成配方数据API或静态数据

## 界面设计说明

### 1. 模式切换设计
- 在现有输入框右侧添加模式切换按钮
- 类似豆包的圆形按钮，图标为工作台图标
- 支持一键切换：普通对话模式 ↔ 合成导航模式

### 2. 分屏布局设计
- **左侧对话区域(40%)**：保持现有聊天界面
- **右侧合成区域(60%)**：显示合成树可视化
- **分割线**：可拖拽调整左右比例
- **响应式**：移动端自动切换为上下布局

### 3. 合成树可视化设计
- **节点样式**：Minecraft九宫格工作台样式
- **层级结构**：树状布局，支持展开/折叠
- **交互效果**：悬停高亮、点击展开、拖拽排序
- **物品图标**：使用Minecraft物品贴图

### 4. 物品引用功能
- 点击任意物品节点触发@引用
- 在输入框中以@物品名形式显示
- 支持批量选择和删除引用

## 实现步骤详细规划

### 阶段1：基础架构搭建 (1-10步)

#### 步骤1：创建合成模式的类型定义
**任务**: 在`src/types/index.ts`中新增合成相关的TypeScript类型定义
**具体内容**:
```typescript
// 合成配方相关类型
export interface CraftingItem {
  id: string;
  name: string;
  displayName: string;
  icon?: string;
  description?: string;
}

export interface CraftingRecipe {
  id: string;
  result: CraftingItem;
  ingredients: (CraftingItem | null)[][];  // 3x3网格
  craftingType: 'crafting_table' | 'furnace' | 'brewing_stand' | 'anvil';
}

export interface CraftingNode {
  item: CraftingItem;
  recipe?: CraftingRecipe;
  children: CraftingNode[];
  depth: number;
  isExpanded: boolean;
  canCraft: boolean;
}

export interface CraftingModeState {
  isActive: boolean;
  currentQuery: string;
  craftingTree: CraftingNode | null;
  selectedItems: CraftingItem[];
  layoutRatio: number; // 左右分屏比例
}
```

#### 步骤2：扩展Vuex Store状态管理
**任务**: 在`src/store/index.ts`中添加合成模式的状态管理
**具体内容**:
- 添加craftingMode模块到store
- 实现合成模式的启用/禁用actions
- 实现合成树数据的存储和更新
- 实现选中物品的管理

#### 步骤3：创建模式切换按钮组件
**任务**: 创建`src/components/chat/ModeToggle.vue`组件
**具体内容**:
- 设计Minecraft风格的圆形切换按钮
- 工作台图标和对话气泡图标切换
- 平滑的过渡动画效果
- 支持快捷键切换(Ctrl+M)

#### 步骤4：修改ChatInterface主布局结构
**任务**: 修改`src/components/chat/ChatInterface.vue`支持分屏模式
**具体内容**:
- 添加分屏容器和布局逻辑
- 保持原有聊天区域不变
- 添加可拖拽的分割线组件
- 实现响应式布局切换

#### 步骤5：创建分割线组件
**任务**: 创建`src/components/chat/ResizableDivider.vue`组件
**具体内容**:
- 垂直分割线，支持鼠标拖拽
- Minecraft风格的分割线样式
- 拖拽时的视觉反馈
- 保存用户偏好的分屏比例

#### 步骤6：创建合成树容器组件
**任务**: 创建`src/components/crafting/CraftingPanel.vue`组件
**具体内容**:
- 右侧合成面板的主容器
- 标题栏：显示当前查询的物品名称
- 工具栏：展开全部、折叠全部、清空等操作
- 滚动区域：显示合成树内容

#### 步骤7：创建九宫格工作台组件
**任务**: 创建`src/components/crafting/CraftingGrid.vue`组件
**具体内容**:
- 3x3网格布局，完全还原Minecraft工作台
- 支持显示物品图标和数量
- 悬停显示物品详细信息
- 点击触发物品选择事件

#### 步骤8：创建物品图标组件
**任务**: 创建`src/components/crafting/ItemIcon.vue`组件
**具体内容**:
- 标准化的物品图标显示组件
- 支持不同尺寸：小(16px)、中(32px)、大(48px)
- 加载失败时显示默认图标
- 物品数量角标显示

#### 步骤9：创建合成节点组件
**任务**: 创建`src/components/crafting/CraftingNode.vue`组件
**具体内容**:
- 单个合成节点的完整显示
- 包含工作台网格和结果物品
- 展开/折叠按钮和动画
- 递归渲染子节点

#### 步骤10：准备Minecraft物品数据
**任务**: 创建`src/data/minecraft-items.ts`和`src/data/crafting-recipes.ts`
**具体内容**:
- 整理Minecraft物品清单(至少100个常用物品)
- 整理合成配方数据(至少50个配方)
- 创建物品图标映射关系
- 创建物品搜索索引

### 阶段2：核心功能实现 (11-25步)

#### 步骤11：实现合成树算法
**任务**: 创建`src/services/crafting.ts`合成计算服务
**具体内容**:
- 根据目标物品生成完整合成树
- 递归解析所有依赖物品的配方
- 优化算法避免循环依赖
- 支持多种合成方式的选择

#### 步骤12：集成模式切换到ChatInterface
**任务**: 在ChatInterface中集成模式切换功能
**具体内容**:
- 在输入区域添加ModeToggle组件
- 监听模式切换事件
- 动态显示/隐藏合成面板
- 保持对话数据不丢失

#### 步骤13：实现分屏布局逻辑
**任务**: 完善ChatInterface的分屏布局
**具体内容**:
- 默认分屏比例为40:60
- 支持分割线拖拽调整
- 最小宽度限制防止界面挤压
- 分屏状态持久化存储

#### 步骤14：实现合成查询处理
**任务**: 在ChatInterface中处理合成模式的查询
**具体内容**:
- 监听用户输入的物品名称
- 调用合成计算服务生成树结构
- 更新Vuex store中的合成树数据
- 显示查询结果到合成面板

#### 步骤15：实现物品@引用功能
**任务**: 创建物品引用输入增强功能
**具体内容**:
- 点击合成树中的物品触发@事件
- 在输入框中插入@物品名标签
- 支持多个物品的@引用
- 引用标签的样式设计和删除功能

#### 步骤16：创建物品详情弹窗
**任务**: 创建`src/components/crafting/ItemDetail.vue`组件
**具体内容**:
- 显示物品的详细信息
- 包含获取方式、用途、相关配方
- Minecraft风格的弹窗设计
- 支持从多个位置触发显示

#### 步骤17：实现合成树的展开折叠
**任务**: 完善CraftingNode组件的交互功能
**具体内容**:
- 节点点击展开/折叠动画
- 批量展开和折叠操作
- 展开状态的记忆功能
- 性能优化，延迟渲染大型树结构

#### 步骤18：实现合成树搜索功能
**任务**: 在CraftingPanel中添加搜索功能
**具体内容**:
- 搜索框组件，支持物品名称模糊匹配
- 高亮显示匹配的物品节点
- 快速定位到搜索结果
- 搜索历史记录功能

#### 步骤19：实现合成树导出功能
**任务**: 添加合成清单导出功能
**具体内容**:
- 生成所需原材料清单
- 支持导出为文本或JSON格式
- 计算总体资源需求
- 一键复制到剪贴板

#### 步骤20：优化合成树渲染性能
**任务**: 实现虚拟滚动和延迟加载
**具体内容**:
- 大型合成树的虚拟滚动
- 节点懒加载，减少初始渲染时间
- 图片懒加载优化
- 内存泄漏防护

#### 步骤21：实现合成模式的快捷操作
**任务**: 添加键盘快捷键支持
**具体内容**:
- Ctrl+M: 切换模式
- Ctrl+F: 聚焦搜索框
- Ctrl+E: 展开所有节点
- Ctrl+R: 折叠所有节点
- ESC: 退出合成模式

#### 步骤22：集成自然语言处理
**任务**: 增强查询解析能力
**具体内容**:
- 支持自然语言物品查询
- 智能纠错和建议
- 同义词和别名识别
- 查询意图理解

#### 步骤23：实现合成路径优化
**任务**: 提供多种合成策略
**具体内容**:
- 最少步骤路径
- 最少资源消耗路径
- 最快获得路径
- 用户自定义偏好路径

#### 步骤24：添加合成历史记录
**任务**: 实现查询历史管理
**具体内容**:
- 保存最近查询的物品
- 快速重新查询历史物品
- 收藏常用合成配方
- 历史记录的云端同步

#### 步骤25：完善错误处理和加载状态
**任务**: 添加完整的错误处理机制
**具体内容**:
- 物品不存在的友好提示
- 网络错误的重试机制
- 加载状态的动画效果
- 降级方案和离线支持

### 阶段3：用户体验优化 (26-40步)

#### 步骤26：实现主题自适应
**任务**: 确保合成界面与现有Minecraft主题一致
**具体内容**:
- 统一色彩方案和字体
- 适配深色/浅色模式
- 保持像素化的视觉风格
- 响应式设计适配

#### 步骤27：添加操作引导和帮助
**任务**: 创建新手引导和帮助文档
**具体内容**:
- 首次使用的操作引导
- 功能说明的tooltip提示
- 快捷键操作指南
- 常见问题解答

#### 步骤28：实现拖拽排序功能
**任务**: 支持合成步骤的拖拽重排
**具体内容**:
- 合成节点的拖拽移动
- 自定义合成顺序
- 拖拽时的视觉反馈
- 排序结果的持久化

#### 步骤29：添加合成计算器
**任务**: 创建资源需求计算工具
**具体内容**:
- 输入目标数量，计算所需原料
- 考虑合成剩余物品
- 批量合成优化建议
- 成本效益分析

#### 步骤30：实现合成分享功能
**任务**: 支持合成方案的分享
**具体内容**:
- 生成分享链接
- 二维码分享
- 社交媒体集成
- 分享内容的可视化预览

#### 步骤31：添加动画和过渡效果
**任务**: 丰富界面的动画效果
**具体内容**:
- 模式切换的平滑过渡
- 合成树展开的动画
- 物品添加的反馈动画
- 微交互的细节动画

#### 步骤32：实现个性化设置
**任务**: 添加用户偏好设置
**具体内容**:
- 默认分屏比例设置
- 自动展开深度设置
- 物品图标显示偏好
- 快捷键自定义

#### 步骤33：优化移动端体验
**任务**: 完善移动设备适配
**具体内容**:
- 触控操作优化
- 上下分屏布局
- 手势导航支持
- 移动端专用的交互模式

#### 步骤34：添加离线缓存功能
**任务**: 实现离线数据支持
**具体内容**:
- 常用配方的本地缓存
- 离线模式的功能降级
- 数据同步策略
- 缓存更新机制

#### 步骤35：实现多语言支持
**任务**: 添加国际化功能
**具体内容**:
- 界面文本的多语言
- 物品名称的本地化
- 语言切换功能
- RTL语言支持

#### 步骤36：添加可访问性支持
**任务**: 改善无障碍访问体验
**具体内容**:
- 键盘导航支持
- 屏幕阅读器兼容
- 高对比度模式
- 焦点管理优化

#### 步骤37：实现数据统计分析
**任务**: 添加使用情况统计
**具体内容**:
- 查询频率统计
- 用户行为分析
- 性能指标监控
- 错误日志收集

#### 步骤38：创建API接口对接
**任务**: 对接后端合成数据API
**具体内容**:
- RESTful API设计
- 数据获取和缓存策略
- 错误处理和重试机制
- API版本管理

#### 步骤39：实现实时协作功能
**任务**: 支持多用户协作查看
**具体内容**:
- 实时分享合成方案
- 协作标注和讨论
- 版本控制和冲突解决
- 权限管理

#### 步骤40：完整测试和文档
**任务**: 完善测试覆盖和文档
**具体内容**:
- 单元测试编写
- 集成测试验证
- 用户手册编写
- 开发文档完善

## 技术难点和解决方案

### 1. 合成树算法复杂度
**问题**: 深层递归和循环依赖处理
**解决方案**: 
- 使用记忆化算法避免重复计算
- 设置最大递归深度防止栈溢出
- 循环依赖检测和处理机制

### 2. 大型树结构渲染性能
**问题**: 大量DOM节点影响性能
**解决方案**:
- 虚拟滚动技术
- 节点懒加载
- React式的差异更新

### 3. 分屏布局响应式设计
**问题**: 多种屏幕尺寸适配
**解决方案**:
- CSS Grid和Flexbox结合
- 媒体查询断点设计
- 动态布局切换

### 4. 物品图标资源管理
**问题**: 大量图片资源加载
**解决方案**:
- 图片懒加载
- Sprite图优化
- WebP格式支持

## 数据需求

### 物品数据结构
```json
{
  "id": "minecraft:diamond_sword",
  "name": "diamond_sword",
  "displayName": "钻石剑",
  "icon": "/icons/diamond_sword.png",
  "description": "锋利的钻石制武器",
  "category": "weapons",
  "rarity": "uncommon"
}
```

### 合成配方数据结构
```json
{
  "id": "diamond_sword_recipe",
  "result": {
    "item": "minecraft:diamond_sword",
    "count": 1
  },
  "pattern": [
    [null, "minecraft:diamond", null],
    [null, "minecraft:diamond", null],
    [null, "minecraft:stick", null]
  ],
  "type": "crafting_table"
}
```

## 项目时间估算

- **阶段1 (基础架构)**: 2-3周
- **阶段2 (核心功能)**: 3-4周  
- **阶段3 (体验优化)**: 2-3周
- **总计**: 7-10周

## 质量保证

1. **代码规范**: 遵循现有的ESLint配置
2. **类型安全**: 完整的TypeScript类型定义
3. **测试覆盖**: 关键功能的单元测试
4. **性能监控**: 加载时间和内存使用监控
5. **用户反馈**: 集成现有的反馈系统

## 后期扩展规划

1. **3D可视化**: 使用Three.js实现3D合成演示
2. **AR功能**: 移动端AR合成指导
3. **AI助手**: 智能合成建议和优化
4. **社区功能**: 用户分享和评分系统
5. **模组支持**: 扩展支持模组物品和配方

---

此规划确保每个步骤都足够明确和可实现，每次只需要一个清晰的prompt就能让Copilot完成对应的任务。整个功能将完美融入现有的Minecraft主题RAG应用中。
