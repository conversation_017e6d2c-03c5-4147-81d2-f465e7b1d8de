<!-- 消息组件，用于显示用户或助手的消息 -->
<template>
  <div class="message" :class="message.role">
    <!-- user消息靠右显示，添加Steve头像 -->
    <div v-if="message.role === 'user'" class="message-container user-message">
      <div class="message-content">
        <div class="message-text">{{ message.content }}</div>
        <div class="message-time">{{ formatTime(message.created_at) }}</div>
      </div>
      <div class="message-avatar user"></div>
    </div>
    <!-- assistant消息靠左显示 -->
    <div v-else class="message-container assistant-message">
      <div class="message-avatar assistant"></div>
      <div class="message-content" :class="{ 'loading': message.isLoading, 'error': message.isError }">
        <!-- 合成导航卡片+文字合并在同一气泡 -->
        <template v-if="message.type === 'CRAFTING'">
          <div style="margin-bottom: 18px;">
            <CraftingCard
              :craftingData="message.craftingData"
              :description="''"
              :isSplitPane="isSplitPane"
              @expand-split="$emit('expand-split', message)"
              @select-item="itemName => { console.log('[Message] select-item', itemName); emit('select-item', itemName) }"
            />
          </div>
          <div
            ref="messageTextRef"
            class="message-text"
            v-if="message.content"
            v-html="markdownToHtml(message.content)"
            :key="contentKey"
          ></div>
        </template>
        <!-- 正在加载状态 -->
        <template v-else-if="message.isLoading">
          <div
            ref="messageTextRef"
            class="message-text"
            v-if="message.content"
            v-html="markdownToHtml(message.content)"
            :key="contentKey"
          ></div>
          <slot name="loading"></slot>
        </template>
        <!-- 错误状态 -->
        <template v-else-if="message.isError">
          <div
            ref="messageTextRef"
            class="message-text error-text"
            v-html="markdownToHtml(message.content || '')"
            :key="contentKey"
          ></div>
        </template>
        <!-- 正常显示状态 -->
        <template v-else>
          <div
            ref="messageTextRef"
            class="message-text"
            v-html="markdownToHtml(message.content)"
            :key="contentKey"
          ></div>
        </template>
        <div class="message-time">{{ formatTime(message.created_at) }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType, ref, watch, nextTick } from 'vue';
import { Message } from '@/types';
import { marked } from 'marked';
import CraftingCard from './CraftingCard.vue';
import { forceRefreshFont } from '@/utils/fontUtils';
// CraftingRecipeCard is used within CraftingCard, so no direct import needed here if events are bubbled

const props = defineProps({
  message: {
    type: Object as PropType<Message>,
    required: true
  },
  isSplitPane: {
    type: Boolean,
    default: false
  }
});

// Define emits to relay the select-item event
const emit = defineEmits(['expand-split', 'select-item']);

// 用于强制重新渲染的key
const contentKey = ref(0);
const messageTextRef = ref<HTMLElement | null>(null);

// 监听消息加载状态变化，当从加载状态变为完成状态时强制重新渲染
watch(() => props.message.isLoading, (newLoading, oldLoading) => {
  if (oldLoading && !newLoading) {
    // 从加载状态变为完成状态，强制重新渲染以确保字体正确应用
    nextTick(() => {
      contentKey.value++;
      // 使用工具函数强制刷新字体
      if (messageTextRef.value) {
        forceRefreshFont(messageTextRef.value);
      }
    });
  }
});

// 监听消息内容变化，在流式更新过程中也定期刷新渲染
watch(() => props.message.content, () => {
  if (props.message.isLoading) {
    // 在流式更新过程中，定期强制刷新字体渲染
    nextTick(() => {
      if (messageTextRef.value) {
        forceRefreshFont(messageTextRef.value);
      }
    });
  }
});

const markdownToHtml = (markdown: string): string => {
  if (!markdown) return '';
  try {
    const rawHtml = marked.parse(markdown);
    if (typeof rawHtml === 'string') {
      return rawHtml.replace(/<a\s+(?:[^>]*?\s+)?href=(["'])(.*?)\1/g,
        '<a target="_blank" rel="noopener noreferrer" href=$1$2$1');
    }
    return markdown;
  } catch (error) {
    console.error('Markdown渲染失败:', error);
    return markdown;
  }
};

const formatTime = (timestamp?: string) => {
  if (!timestamp) return '';
  return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};
</script>

<style scoped>
.message {
  width: 100%;
  margin-bottom: 16px;
  animation: fadeIn 0.3s ease;
}

.message-container {
  display: flex;
  max-width: 85%;
  align-items: flex-start;
}

.user-message {
  margin-left: auto;
  margin-right: 0;
  justify-content: flex-end;
  width: 100%;
}

.assistant-message {
  margin-right: auto;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  flex-shrink: 0;
  margin: 0 8px;
}

.message-avatar.user {
  background-image: url('@/assets/steve.jpg');
  background-size: cover;
  background-position: center;
}

.message-avatar.assistant {
  background-image: url('@/assets/villager.jpg');
  background-size: cover;
  background-position: center;
}

.message-content {
  padding: 12px 16px;
  border-radius: 16px;
  position: relative;
  max-width: calc(100% - 52px);
  display: inline-block;
}

.user-message .message-content {
  background-color: #455A64;
  border-top-right-radius: 2px;
  max-width: 85%;
}

.assistant-message .message-content {
  background-color: #2E7D32;
  border-top-left-radius: 2px;
  margin-left: 4px;
}

.message-text {
  word-break: break-word;
  line-height: 1.5;
  font-family: 'Minecraft', 'Consolas', 'Monaco', 'Courier New', monospace;
  color: #ffffff;

  /* 强制数字使用Minecraft字体 */
  font-variant-numeric: normal;
  font-feature-settings: normal;
}

/* 确保所有数字字符使用正确的字体 */
.message-text :deep(*) {
  font-family: inherit !important;
}

/* 特别针对数字的字体强制应用 */
.message-text :deep(p),
.message-text :deep(span),
.message-text :deep(div),
.message-text :deep(li),
.message-text :deep(td),
.message-text :deep(th),
.message-text :deep(h1),
.message-text :deep(h2),
.message-text :deep(h3),
.message-text :deep(h4),
.message-text :deep(h5),
.message-text :deep(h6) {
  font-family: 'Minecraft', 'Consolas', 'Monaco', 'Courier New', monospace !important;
  font-variant-numeric: normal !important;
  font-feature-settings: normal !important;
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 6px;
  text-align: right;
}

.message-content.error {
  background-color: rgba(255, 85, 85, 0.2);
  border: 1px solid #ff5555;
}

.error-text {
  color: #ff5555;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
