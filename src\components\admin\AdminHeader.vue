<template>
  <header class="admin-header">
    <img src="@/assets/creeper.webp" class="creeper-icon" alt="Creeper" />
    <h1>Minecrag后台管理</h1>
    <div class="admin-user-info" v-if="currentUser">
      <span class="username">{{ currentUser.username }}</span>
      <button class="logout-button" @click="logout" title="退出登录">
        <span class="material-icons">logout</span>
      </button>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';

const store = useStore();
const router = useRouter();
const currentUser = computed(() => store.state.user);

const logout = () => {
  store.dispatch('logout');
  router.push('/login');
};
</script>

<style scoped lang="scss">
.admin-header {
  width: 100%;
  background: #111;
  padding: 12px 0 12px 0;
  text-align: left;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  position: relative;
  user-select: none;
}
.creeper-icon {
  width: 36px;
  height: 36px;
  margin-left: 24px;
  margin-right: 24px;
  vertical-align: middle;
  filter: drop-shadow(0 0 8px #55ff55cc);
  user-select: none;
  pointer-events: none;
}
.admin-header h1 {
  color: #55ff55;
  font-size: 1.7rem;
  font-family: 'Minecraft', monospace;
  margin: 0;
  text-shadow: 2px 2px 8px #000, 0 0 8px #55ff5588;
  display: inline-block;
  vertical-align: middle;
  letter-spacing: 2px;
  user-select: none;
}
.admin-user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  position: absolute;
  right: 32px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(40,60,40,0.10);
  border-radius: 6px;
  padding: 4px 12px 4px 12px;
  user-select: none;
}
.username {
  color: #3498db;
  font-weight: bold;
  font-size: 14px;
  letter-spacing: 1px;
  user-select: none;
}
.logout-button {
  background: none;
  border: none;
  color: #aaaaaa;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s;
  user-select: none;
}
.logout-button:hover {
  color: #55ff55;
  background-color: rgba(85, 255, 85, 0.1);
}
</style>
