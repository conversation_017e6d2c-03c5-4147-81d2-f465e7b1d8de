<template>
  <div class="crafting-recipe-card">
    <div class="recipe-grid">
      <div v-for="(row, i) in item.recipe" :key="i" class="recipe-row">
        <div v-for="(cell, j) in row" :key="j" class="recipe-cell">
          <div
            v-if="cell"
            class="cell-item"
            @mouseenter="showTooltip($event, i, j, cell)"
            @mouseleave="hideTooltip"
            @click.stop="onSelectItem(cell)"
            :ref="(el) => assignCellRef(i, j, el as HTMLElement | null)"
          >
            <img :src="getItemIcon(cell)" class="cell-icon" />
            <!-- Tooltip div removed from here -->
          </div>
        </div>
      </div>
    </div>
    <div class="recipe-footer">
      <div
        class="output-icon-wrapper"
        @mouseenter="showTooltip($event, 'output', 0, item.itemId)"
        @mouseleave="hideTooltip"
        @click.stop="onSelectItem(item.itemId)"
        ref="outputIconRef"
      >
        <img
          class="output-icon"
          :src="item.itemIcon"
          :title="item.itemName"
        />
        <div
          v-if="tooltip.show && tooltip.i === 'output'"
          class="craft-tooltip mc-bubble"
          :style="{...tooltipPosStyle, zIndex: 3000}"
          @mouseenter="clearHideTimeout"
          @mouseleave="hideTooltip"
        >
          <span
            class="tooltip-insert"
            title="点击插入到输入上下文"
            @click.stop="onSelectItem(item.itemId)"
            >@
          </span>
          <div class="tooltip-title">{{ item.itemName }}</div>
          <div class="tooltip-mod">{{ getItemMod(item.itemId) }}</div>
          <div class="tooltip-arrow"></div>
        </div>
      </div>
      <div class="recipe-title">{{ item.itemName }}</div>
    </div>

    <!-- Single tooltip for grid items -->
    <div
      v-if="tooltip.show && typeof tooltip.i === 'number' && typeof tooltip.j === 'number' && tooltip.id !== null"
      class="craft-tooltip mc-bubble"
      :style="tooltipCellPosStyle"
      @mouseenter="clearHideTimeout"
      @mouseleave="hideTooltip"
    >
      <span class="tooltip-insert" title="点击插入到输入上下文" @click.stop="onSelectItem(tooltip.id!)">@</span>
      <div class="tooltip-title">{{ getItemName(tooltip.id!) }}</div>
      <div class="tooltip-mod">{{ getItemMod(tooltip.id!) }}</div>
      <div class="tooltip-arrow"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, type CSSProperties, nextTick } from 'vue';
import type { CraftingTreeNodeData } from '@/types';
const props = defineProps<{ item: CraftingTreeNodeData }>();
const emit = defineEmits(['select-item']);

const tooltip = ref<{ show: boolean; i: number|string|null; j: number|null; id: string|null }>({ show: false, i: null, j: null, id: null });
const cellRefs = ref<Record<string, HTMLElement | null>>({});
const outputIconRef = ref<HTMLElement | null>(null);
let hideTimeoutId = ref<number | null>(null);

function assignCellRef(i: number, j: number, el: HTMLElement | null) {
  const key = `${i},${j}`;
  if (el) {
    cellRefs.value[key] = el;
  } else {
    // If element is unmounted, set its ref to null in the map
    if (cellRefs.value.hasOwnProperty(key)) {
      cellRefs.value[key] = null;
    }
  }
}

// getCellRef function is replaced by assignCellRef

// 九宫格内tooltip用fixed定位，z-index高，三角和气泡与图标有间隙
const tooltipCellPosStyle = computed(() => {
  if (!tooltip.value.show || typeof tooltip.value.i !== 'number' || typeof tooltip.value.j !== 'number') {
    return { display: 'none' } as CSSProperties;
  }
  const key = `${tooltip.value.i},${tooltip.value.j}`;
  const el = cellRefs.value[key];

  if (el) {
    const rect = el.getBoundingClientRect();
    // Ensure the element is actually visible and has dimensions
    if (rect.width === 0 && rect.height === 0 && rect.top === 0 && rect.left === 0) {
        return { display: 'none' } as CSSProperties; // Element not visible or detached
    }
    return {
      position: 'fixed',
      left: `${rect.left + rect.width / 2}px`,
      top: `${rect.top - 12}px`,
      transform: 'translate(-50%, -100%)',
      zIndex: 3000
    } as CSSProperties;
  }
  return { display: 'none' };
});

const tooltipPosStyle = computed(() => {
  if (!tooltip.value.show) return { display: 'none' } as CSSProperties;
  let left = 0, top = 0;
  let el: HTMLElement | null = null;
  if (tooltip.value.i === 'output') {
    el = outputIconRef.value;
    if (el) {
      const rect = el.getBoundingClientRect();
      left = rect.left + rect.width / 2;
      top = rect.top;
      return {
        position: 'fixed' as const,
        left: `${left}px`,
        top: `${top - 8}px`,
        transform: 'translate(-50%, -100%)',
        zIndex: 3000
      } as CSSProperties;
    }
  }
  return { display: 'none' } as CSSProperties;
});

function clearHideTimeout() {
  if (hideTimeoutId.value !== null) {
    clearTimeout(hideTimeoutId.value);
    hideTimeoutId.value = null;
  }
}

function showTooltip(e: MouseEvent, i: number|string, j: number, id: string) {
  console.log('[CraftingRecipeCard] showTooltip id=', id, 'at', i, j);
  clearHideTimeout(); // Clear any pending hide operations
  tooltip.value = { show: true, i, j, id };
}
function hideTooltip() {
  console.log('[CraftingRecipeCard] hideTooltip');
  clearHideTimeout(); // Clear any existing timeout before setting a new one
  hideTimeoutId.value = window.setTimeout(() => {
    tooltip.value = { show: false, i: null, j: null, id: null };
    hideTimeoutId.value = null; // Reset timeoutId after execution
  }, 300); // 300ms delay, adjust as needed
}

// Handle clicking an item or tooltip '@' button
function onSelectItem(id: string) {
  console.log('[CraftingRecipeCard] onSelectItem id=', id);
  emit('select-item', getItemName(id));
  hideTooltip();
}

// 查找节点名称和图标，支持根节点、直接子节点及递归子节点
function findNode(id: string, nodes: CraftingTreeNodeData[]): CraftingTreeNodeData | undefined {
  for (const node of nodes) {
    if (node.itemId === id) return node;
    const found = findNode(id, node.children);
    if (found) return found;
  }
}
function getItemName(id: string): string {
  if (props.item.itemId === id) return props.item.itemName;
  const node = findNode(id, props.item.children);
  return node ? node.itemName : id;
}
function getItemIcon(id: string): string {
  if (props.item.itemId === id) return props.item.itemIcon;
  const node = findNode(id, props.item.children);
  return node ? node.itemIcon : props.item.itemIcon;
}
function getItemMod(id: string): string {
  // 可根据实际数据结构返回模组名，这里用占位
  const node = id === props.item.itemId ? props.item : findNode(id, props.item.children);
  return node && node.itemDesc ? node.itemDesc : 'Minecraft';
}
</script>

<style scoped>
.crafting-recipe-card {
  background: #222;
  border: 2px solid #55ff55;
  border-radius: 10px;
  padding: 12px 16px 8px 16px;
  min-width: 120px;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2px 8px #0008;
}
.recipe-grid {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.recipe-row {
  display: flex;
  gap: 2px;
}
.recipe-cell {
  width: 32px;
  height: 32px;
  background: #333;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cell-item {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.cell-icon {
  width: 20px;
  height: 20px;
  margin-bottom: 2px;
}
.recipe-title {
  margin-top: 8px;
  color: #55ff55;
  font-size: 14px;
  font-family: 'Minecraft', monospace;
  font-weight: bold;
}
.output-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}
.output-icon {
  width: 20px;
  height: 20px;
  cursor: pointer;
  border-radius: 4px;
  border: 1px solid #55ff55;
  box-shadow: 0 0 4px #55ff55;
  margin-top: 4px;
}
.recipe-footer {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
}
.mc-bubble {
  min-width: 110px;
  background: linear-gradient(180deg, #181f1a 80%, #1e2b1e 100%);
  color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 12px #55ff55cc, 0 2px 8px #000a;
  padding: 10px 16px 10px 14px;
  z-index: 1000;
  pointer-events: auto;
  opacity: 1;
  transition: opacity 0.15s;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-family: 'Minecraft', 'Consolas', monospace;
  position: fixed;
  border: 1.5px solid #55ff55;
  filter: drop-shadow(0 0 6px #55ff55cc);
  /* 九宫格和output都用fixed定位，z-index高 */
}
.tooltip-title {
  font-size: 14px;
  font-weight: bold;
  color: #b2ffb2;
  margin-bottom: 2px;
  text-shadow: 0 1px 2px #000a;
}
.tooltip-mod {
  font-size: 12px;
  color: #7ee7c7;
  margin-bottom: 2px;
  text-shadow: 0 1px 2px #000a;
}
.tooltip-insert {
  position: absolute;
  right: 10px;
  top: 8px;
  display: inline-block;
  font-size: 15px;
  color: #55ff55;
  background: #222;
  border-radius: 50%;
  border: 1.5px solid #55ff55;
  width: 18px;
  height: 18px;
  text-align: center;
  line-height: 16px;
  cursor: pointer;
  transition: background 0.15s, color 0.15s;
  box-shadow: 0 0 4px #55ff5544;
}
.tooltip-insert:hover {
  background: #55ff55;
  color: #222;
}
.tooltip-arrow {
  position: absolute;
  left: 50%;
  bottom: -10px;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #1e2b1e;
  filter: drop-shadow(0 0 4px #55ff55cc);
  pointer-events: none;
}
.tooltip-hint {
  font-size: 12px;
  color: #ddd;
  margin-bottom: 6px;
  text-align: right;
  width: 100%;
}
</style>
