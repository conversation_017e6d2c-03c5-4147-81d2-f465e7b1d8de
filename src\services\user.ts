import { AxiosResponse } from 'axios';
import httpService from './httpService';

interface UserResponse {
  user_id: number;
  username: string;
  email: string;
  created_at: string;
  role: 'ADMIN' | 'USER';
}

interface BasicResponse {
  success: boolean;
  message: string;
}

interface QueryItem {
  query_id: number;
  text: string;
  timestamp: string;
  answer: string;
}

interface HistoryResponse {
  queries: QueryItem[];
  total: number;
}

/**
   * 获取当前用户信息
   * @returns {Promise<AxiosResponse<UserResponse>>}
   */
export const getCurrentUser = async (): Promise<AxiosResponse<UserResponse>> => {
  return await httpService.get<UserResponse>('/users/me');
};

/**
   * 更新用户信息（目前在项目中没有用到）
   * @param {Object} updateData - 要更新的数据
   * @returns {Promise<AxiosResponse<BasicResponse>>}
   */
export const updateUserInfo = async (updateData: { username?: string, email?: string, password?: string }): Promise<AxiosResponse<BasicResponse>> => {
  return await httpService.put<BasicResponse>('/users/me', updateData);
};

/**
   * 获取用户历史记录（目前在项目中没有用到）
   * @param {number} limit - 限制条数
   * @param {number} offset - 偏移量
   * @returns {Promise<AxiosResponse<HistoryResponse>>}
   */
export const getUserHistory = async (limit: number = 10, offset: number = 0): Promise<AxiosResponse<HistoryResponse>> => {
  return await httpService.get<HistoryResponse>(`/users/me/history?limit=${limit}&offset=${offset}`);
};
