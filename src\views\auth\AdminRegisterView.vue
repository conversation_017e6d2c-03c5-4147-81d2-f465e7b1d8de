<template>
  <div class="auth-container">
    <div class="auth-box">
      <div class="title">
        <h2>Minecraft 知识助手</h2>
        <h3>管理员注册</h3>
      </div>
      
      <form @submit.prevent="register" class="auth-form">
        <div class="alert error" v-if="error">{{ error }}</div>
        <div class="alert success" v-if="successMessage">{{ successMessage }}</div>
        
        <div class="form-group">
          <label for="username">用户名</label>
          <input 
            type="text" 
            id="username" 
            v-model="username" 
            placeholder="请输入用户名 (字母开头，6-20位)"
            required 
            autocomplete="username"
            :class="{ 'invalid': !usernameValid && username }"
          />
          <div class="field-error" v-if="!usernameValid && username">
            用户名必须以字母开头，6-20个字符
          </div>
        </div>
        
        <div class="form-group">
          <label for="email">电子邮箱</label>
          <input 
            type="email" 
            id="email" 
            v-model="email" 
            placeholder="请输入电子邮箱" 
            required 
            autocomplete="email"
            :class="{ 'invalid': !emailValid && email }"
          />
          <div class="field-error" v-if="!emailValid && email">
            请输入有效的电子邮箱地址
          </div>
        </div>
        
        <div class="form-group">
          <label for="password">密码</label>
          <input 
            type="password" 
            id="password" 
            v-model="password" 
            placeholder="请输入密码 (至少8位)" 
            required 
            autocomplete="new-password"
            :class="{ 'invalid': !passwordValid && password }"
          />
          <div class="field-error" v-if="!passwordValid && password">
            密码长度至少为8位
          </div>
        </div>
        
        <div class="form-group">
          <label for="confirmPassword">确认密码</label>
          <input 
            type="password" 
            id="confirmPassword" 
            v-model="confirmPassword" 
            placeholder="请再次输入密码" 
            required
            autocomplete="new-password"
            :class="{ 'invalid': !passwordsMatch && confirmPassword }"
          />
          <div class="field-error" v-if="!passwordsMatch && confirmPassword">
            两次输入的密码不一致
          </div>
        </div>

        <div class="form-group">
          <label for="adminCode">验证码</label>
          <input 
            type="text" 
            id="adminCode" 
            v-model="adminCode" 
            placeholder="请输入管理员验证码" 
            required
            autocomplete="off"
            :class="{ 'invalid': !adminCodeValid && adminCode }"
          />
          <div class="field-error" v-if="!adminCodeValid && adminCode">
            请输入有效的管理员验证码
          </div>
        </div>
        
        <div class="form-actions">
          <button 
            type="submit" 
            class="submit-button" 
            :disabled="loading || !formValid"
          >
            {{ loading ? '注册中...' : '注册' }}
          </button>
        </div>
        
        <div class="form-footer">
          已有账号？ 
          <router-link to="/login" class="link">立即登录</router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';

const store = useStore();
const router = useRouter();
const username = ref('');
const email = ref('');
const password = ref('');
const confirmPassword = ref('');
const adminCode = ref('');
const successMessage = ref('');

const loading = computed(() => store.state.loading);
const error = computed(() => store.state.error);

// 表单验证
const usernameValid = computed(() => {
  const value = username.value;
  return !value || /^[a-zA-Z][a-zA-Z0-9_]{5,19}$/.test(value);
});

const emailValid = computed(() => {
  const value = email.value;
  return !value || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
});

const passwordValid = computed(() => {
  const value = password.value;
  return !value || value.length >= 8;
});

const passwordsMatch = computed(() => {
  return !confirmPassword.value || confirmPassword.value === password.value;
});

const adminCodeValid = computed(() => {
  // 这里可以自定义验证码规则，暂时只要求非空
  return !!adminCode.value;
});

const formValid = computed(() => {
  return usernameValid.value && 
    emailValid.value && 
    passwordValid.value && 
    passwordsMatch.value &&
    adminCodeValid.value &&
    username.value && 
    email.value && 
    password.value && 
    confirmPassword.value &&
    adminCode.value;
});

const register = async () => {
  if (formValid.value) {
    const success = await store.dispatch('adminRegister', {
      username: username.value,
      email: email.value,
      password: password.value,
      verification: adminCode.value
    });
    if (success) {
      successMessage.value = '注册成功！即将为您跳转到登录页面...';
      // 清空表单
      username.value = '';
      email.value = '';
      password.value = '';
      confirmPassword.value = '';
      adminCode.value = '';
      // 3秒后跳转到登录页
      setTimeout(() => {
        router.push('/login');
      }, 3000);
    }
  }
};
</script>

<style lang="scss" scoped>
.auth-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: url('@/assets/minecraft-background.jpg');
  background-size: cover;
  background-position: center;
  padding: 20px;
}

.auth-box {
  width: 100%;
  max-width: 400px;
  background-color: rgba(0, 0, 0, 0.7);
  border: 2px solid #5e5e5e;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.5s ease;
}

.title {
  text-align: center;
  margin-bottom: 30px;
  
  h2 {
    color: #55ff55;
    font-size: 24px;
    margin-bottom: 10px;
    text-shadow: 2px 2px #000;
    font-family: 'Minecraft', monospace;
  }
  
  h3 {
    color: #ffffff;
    font-size: 18px;
  }
}

.auth-form {
  .form-group {
    margin-bottom: 20px;
    
    label {
      display: block;
      margin-bottom: 8px;
      color: #aaaaaa;
      font-size: 14px;
    }
    
    input {
      width: 100%;
      height: 40px;
      padding: 10px;
      background-color: rgba(255, 255, 255, 0.1);
      border: 2px solid #5e5e5e;
      border-radius: 4px;
      color: #ffffff;
      font-family: 'Minecraft', sans-serif;
      font-size: 14px;
      
      &:focus {
        outline: none;
        border-color: #55ff55;
        box-shadow: 0 0 0 2px rgba(85, 255, 85, 0.3);
      }
      
      &::placeholder {
        color: #777777;
      }
      
      &.invalid {
        border-color: #ff4949;
        box-shadow: 0 0 0 2px rgba(255, 73, 73, 0.3);
      }
    }
    
    .field-error {
      color: #ff7070;
      font-size: 12px;
      margin-top: 5px;
    }
  }
  
  .form-actions {
    margin-top: 30px;
    
    .submit-button {
      width: 100%;
      height: 46px;
      background-color: #55ff55;
      border: none;
      border-radius: 4px;
      color: #000000;
      font-weight: bold;
      cursor: pointer;
      font-family: 'Minecraft', sans-serif;
      font-size: 16px;
      transition: all 0.2s;
      
      &:hover {
        background-color: #7dff7d;
        transform: translateY(-2px);
      }
      
      &:disabled {
        background-color: #777777;
        cursor: not-allowed;
        transform: none;
      }
    }
  }
  
  .form-footer {
    margin-top: 20px;
    text-align: center;
    color: #aaaaaa;
    font-size: 14px;
    
    .link {
      color: #55ff55;
      text-decoration: none;
      cursor: pointer;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.alert {
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 4px;
  
  &.error {
    background-color: rgba(255, 73, 73, 0.2);
    border: 1px solid #ff4949;
    color: #ff7070;
  }
  
  &.success {
    background-color: rgba(85, 255, 85, 0.2);
    border: 1px solid #55ff55;
    color: #55ff55;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 480px) {
  .auth-box {
    padding: 20px;
  }
}
</style>
