<template>
  <div class="json-page">
    <json-viewer :value="item" :expand-depth="2" copyable boxed v-for="item in content" :key="item" class="json-item"/>
    <div class="pagination">
      <button class="page-btn" :disabled="currentPage <= 1" @click="changePage(currentPage - 1)">上一页</button>
      <span class="page-info">{{ currentPage }} / {{ pageCount }}</span>
      <button class="page-btn" :disabled="currentPage >= pageCount" @click="changePage(currentPage + 1)">下一页</button>
      <input class="page-input" type="number" v-model.number="inputPage" :min="1" :max="pageCount" @keydown.enter="jumpToPage" />
      <button class="page-btn" @click="jumpToPage" :disabled="!isInputValid">跳转</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, watch, computed } from 'vue';

type Props = {
  content: any[];
  currentPage: number;
  pageCount: number;
};

const props = defineProps<Props>();
const emit = defineEmits(['update:currentPage']);

const inputPage = ref(props.currentPage);

watch(() => props.currentPage, (val) => {
  inputPage.value = val;
});

function changePage(page: number) {
  if (page >= 1 && page <= props.pageCount) {
    emit('update:currentPage', page);
    setTimeout(() => {
      const el = document.querySelector('.admin-content');
      if (el) el.scrollTop = 0;
    }, 0);
  }
}

function jumpToPage() {
  let page = Number(inputPage.value);
  if (!Number.isInteger(page) || page < 1 || page > props.pageCount) {
    inputPage.value = props.currentPage;
    return;
  }
  changePage(page);
}

const isInputValid = computed(() => {
  const page = Number(inputPage.value);
  return Number.isInteger(page) && page >= 1 && page <= props.pageCount;
});
</script>

<style scoped>
.json-page {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  color: #fff;
  min-height: 200px;
}
.json-item {
  background: #fff;
  border-radius: 8px;
  margin: 8px 0;
  color: #fff;
}
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
}
.page-btn {
  background: #55ff55;
  color: #23272a;
  border: none;
  border-radius: 4px;
  padding: 6px 18px;
  font-family: 'Minecraft', monospace;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.page-btn:disabled {
  background: #ccc;
  color: #888;
  cursor: not-allowed;
}
.page-info {
  color: #23272a;
  font-size: 1.1rem;
  font-family: 'Minecraft', monospace;
  background: #e0ffe0;
  border-radius: 4px;
  padding: 2px 12px;
}
.page-input {
  width: 75px;
  padding: 4px 8px;
  border: 1.5px solid #55ff55;
  border-radius: 4px;
  font-size: 1rem;
  font-family: 'Minecraft', monospace;
  color: #23272a;
  background: #e0ffe0;
  outline: none;
  text-align: center;
  transition: border 0.2s;
}
.page-input:focus {
  border-color: #7dff7d;
}
</style>
