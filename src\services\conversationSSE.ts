import { fetchEventSource, EventSourceMessage } from '@microsoft/fetch-event-source';
import { mockTree } from './craftingTree';

interface ConversationSSECallbacks {
  onOpen?: (response: Response) => void;
  onMessageChunk?: (chunk: string) => void;
  onEnd?: () => void;
  onError?: (msg: string) => void;
  onConversationStart?: (data: { conversationId: number; type: 'NORMAL' | 'SYNTHETIC_NAVIGATION'; title: string }) => void;
  onTitleUpdate?: (title: string) => void;
}

// Mock switch for SSE
const USE_SSE_MOCK = true;

// 新建会话的SSE流
export function startNewConversationSSE({
  message,
  type = 'NORMAL',
  token,
  callbacks
}: {
  message: string;
  type?: 'NORMAL' | 'SYNTHETIC_NAVIGATION';
  token: string;
  callbacks: ConversationSSECallbacks & {
    onCraftingContext?: (ctx: any) => void;
    onMessageComplete?: (data: any) => void;
  };
}): AbortController {
  const ctrl = new AbortController();
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'text/event-stream',
    'Authorization': `Bearer ${token}`
  };
  const requestBody = JSON.stringify({ message, type });
  const endpointUrl = `/api/conversations/new`;

  fetchEventSource(endpointUrl, {
    method: 'POST',
    headers,
    body: requestBody,
    signal: ctrl.signal,
    openWhenHidden: true,
    onopen: async (response) => {
      if (response.ok) {
        callbacks.onOpen?.(response);
      } else {
        const errorText = await response.text();
        callbacks.onError?.(`连接错误: ${response.status}`);
        ctrl.abort();
      }
    },
    onmessage: (event: EventSourceMessage) => {
      if (event.event === 'end') {
        callbacks.onEnd?.();
        ctrl.abort();
        return;
      }
      if (event.event === 'error') {
        try {
          const errorData = JSON.parse(event.data);
          callbacks.onError?.(`抱歉，处理时发生错误：${errorData.message}`);
        } catch (e) {
          callbacks.onError?.('抱歉，处理时发生未知错误。');
        }
        ctrl.abort();
        return;
      }
      switch (event.event) {
        case 'conversation_start': {
          try {
            const data = JSON.parse(event.data);
            // API sends conversationId, type, title
            callbacks.onConversationStart?.({
              conversationId: data.conversationId,
              type: data.type,
              title: data.title
            });
          } catch {}
          break;
        }
        case 'crafting_context': {
          try {
            const ctx = JSON.parse(event.data);
            callbacks.onCraftingContext?.(ctx);
          } catch (e) {}
          break;
        }
        case 'message': {
          try {
            const msg = JSON.parse(event.data);
            callbacks.onMessageChunk?.(msg.chunk);
          } catch {
            callbacks.onMessageChunk?.(event.data);
          }
          break;
        }
        case 'message_complete': {
          try {
            const completeData = JSON.parse(event.data);
            callbacks.onMessageComplete?.({
              messageId: completeData.messageId,
              type: completeData.type,
              item_name: completeData.item_name
            });
          } catch {}
          break;
        }
        default:
          if (event.data) {
            try {
              const msg = JSON.parse(event.data);
              callbacks.onMessageChunk?.(msg.chunk ?? event.data);
            } catch {
              callbacks.onMessageChunk?.(event.data);
            }
          }
      }
    },
    onclose: () => {
      callbacks.onEnd?.();
    },
    onerror: (err) => {
      callbacks.onError?.('网络连接错误或服务器无响应。');
      throw err;
    }
  });
  return ctrl;
}

// 追加消息到已有会话的SSE流
export function addMessageToConversationSSE({
  conversationId,
  message,
  type,
  token,
  callbacks
}: {
  conversationId: string;
  message: string;
  type?: string;
  token: string;
  callbacks: ConversationSSECallbacks & {
    // onCraftingContext?: (ctx: any) => void; // <-- Remove this callback option
    onMessageComplete?: (data: any) => void;
  };
}): AbortController {
  const ctrl = new AbortController();
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'text/event-stream',
    'Authorization': `Bearer ${token}`
  };
  const requestBody = JSON.stringify({ content: message }); // 修复：字段名改为 content
  const endpointUrl = `/api/conversations/${conversationId}/messages`;

  fetchEventSource(endpointUrl, {
    method: 'POST',
    headers,
    body: requestBody,
    signal: ctrl.signal,
    openWhenHidden: true,
    onopen: async (response) => {
      if (response.ok) {
        callbacks.onOpen?.(response);
      } else {
        const errorText = await response.text();
        callbacks.onError?.(`连接错误: ${response.status}`);
        ctrl.abort();
      }
    },
    onmessage: (event: EventSourceMessage) => {
      if (event.event === 'end') {
        callbacks.onEnd?.();
        ctrl.abort();
        return;
      }
      if (event.event === 'error') {
        try {
          const errorData = JSON.parse(event.data);
          callbacks.onError?.(`抱歉，处理时发生错误：${errorData.message}`);
        } catch (e) {
          callbacks.onError?.('抱歉，处理时发生未知错误。');
        }
        ctrl.abort();
        return;
      }
      switch (event.event) {
        // --- Remove case for crafting_context ---
        // case 'crafting_context': {
        //   try {
        //     const ctx = JSON.parse(event.data);
        //     callbacks.onCraftingContext?.(ctx);
        //   } catch (e) {}
        //   break;
        // }
        // --- End removed case ---
        case 'message': {
          try {
            const msg = JSON.parse(event.data);
            callbacks.onMessageChunk?.(msg.chunk);
          } catch {
            callbacks.onMessageChunk?.(event.data);
          }
          break;
        }
        case 'message_complete': {
          try {
            const completeData = JSON.parse(event.data);
            callbacks.onMessageComplete?.({
              messageId: completeData.messageId,
              type: completeData.type,
              item_name: completeData.item_name
            });
          } catch {}
          break;
        }
        default:
          if (event.data) {
            try {
              const msg = JSON.parse(event.data);
              callbacks.onMessageChunk?.(msg.chunk ?? event.data);
            } catch {
              callbacks.onMessageChunk?.(event.data);
            }
          }
      }
    },
    onclose: () => {
      callbacks.onEnd?.();
    },
    onerror: (err) => {
      callbacks.onError?.('网络连接错误或服务器无响应。');
      throw err;
    }
  });
  return ctrl;
}
