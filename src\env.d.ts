/// <reference types="vite/client" />

/* 声明Vue模块 */
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  // 使用更严格的类型替代 {}, {}, any
  const component: DefineComponent<Record<string, unknown>, Record<string, unknown>, unknown>
  export default component
}

/* 声明静态资源模块 */
declare module '*.jpg' {
  const src: string
  export default src
}

declare module '*.jpeg' {
  const src: string
  export default src
}

declare module '*.png' {
  const src: string
  export default src
}

declare module '*.webp' {
  const src: string
  export default src
}

declare module '*.svg' {
  const src: string
  export default src
}

declare module '*.ttf' {
  const src: string
  export default src
}