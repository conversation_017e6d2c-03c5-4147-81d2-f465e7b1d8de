<template>
  <div class="auth-container">
    <div class="auth-box">
      <div class="title">
        <h2>Minecraft 知识助手</h2>
        <h3>用户登录</h3>
      </div>
      
      <form @submit.prevent="login" class="auth-form">
        <div class="alert error" v-if="error">{{ error }}</div>
        
        <div class="form-group">
          <label for="username">用户名</label>
          <input 
            type="text" 
            id="username" 
            v-model="username" 
            placeholder="请输入用户名" 
            required 
            autocomplete="username"
          />
        </div>
        
        <div class="form-group">
          <label for="password">密码</label>
          <input 
            type="password" 
            id="password" 
            v-model="password" 
            placeholder="请输入密码" 
            required 
            autocomplete="current-password"
          />
        </div>
        
        <div class="form-actions">
          <button 
            type="submit" 
            class="submit-button" 
            :disabled="loading"
          >
            {{ loading ? '登录中...' : '登录' }}
          </button>
        </div>
        
        <div class="form-footer">
          还没有账号？ 
          <router-link to="/register" class="link">立即注册</router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';

const store = useStore();
const router = useRouter();
const username = ref('');
const password = ref('');

const loading = computed(() => store.state.loading);
const error = computed(() => store.state.error);

const login = async () => {
  if (username.value && password.value) {
    const success = await store.dispatch('login', {
      username: username.value,
      password: password.value
    });
    if (success) {
      router.push('/');
    }
  }
};
</script>

<style lang="scss" scoped>
.auth-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: url('@/assets/minecraft-background.jpg');
  background-size: cover;
  background-position: center;
  padding: 20px;
}

.auth-box {
  width: 100%;
  max-width: 400px;
  background-color: rgba(0, 0, 0, 0.7);
  border: 2px solid #5e5e5e;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.5s ease;
}

.title {
  text-align: center;
  margin-bottom: 30px;
  
  h2 {
    color: #55ff55;
    font-size: 24px;
    margin-bottom: 10px;
    text-shadow: 2px 2px #000;
    font-family: 'Minecraft', monospace;
  }
  
  h3 {
    color: #ffffff;
    font-size: 18px;
  }
}

.auth-form {
  .form-group {
    margin-bottom: 20px;
    
    label {
      display: block;
      margin-bottom: 8px;
      color: #aaaaaa;
      font-size: 14px;
    }
    
    input {
      width: 100%;
      height: 40px;
      padding: 10px;
      background-color: rgba(255, 255, 255, 0.1);
      border: 2px solid #5e5e5e;
      border-radius: 4px;
      color: #ffffff;
      font-family: 'Minecraft', sans-serif;
      font-size: 14px;
      
      &:focus {
        outline: none;
        border-color: #55ff55;
        box-shadow: 0 0 0 2px rgba(85, 255, 85, 0.3);
      }
      
      &::placeholder {
        color: #777777;
      }
    }
  }
  
  .form-actions {
    margin-top: 30px;
    
    .submit-button {
      width: 100%;
      height: 46px;
      background-color: #55ff55;
      border: none;
      border-radius: 4px;
      color: #000000;
      font-weight: bold;
      cursor: pointer;
      font-family: 'Minecraft', sans-serif;
      font-size: 16px;
      transition: all 0.2s;
      
      &:hover {
        background-color: #7dff7d;
        transform: translateY(-2px);
      }
      
      &:disabled {
        background-color: #777777;
        cursor: not-allowed;
        transform: none;
      }
    }
  }
  
  .form-footer {
    margin-top: 20px;
    text-align: center;
    color: #aaaaaa;
    font-size: 14px;
    
    .link {
      color: #55ff55;
      text-decoration: none;
      cursor: pointer;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.alert {
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 4px;
  
  &.error {
    background-color: rgba(255, 73, 73, 0.2);
    border: 1px solid #ff4949;
    color: #ff7070;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 480px) {
  .auth-box {
    padding: 20px;
  }
}
</style>