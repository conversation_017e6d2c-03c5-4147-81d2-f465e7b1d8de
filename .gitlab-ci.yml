stages:
  - install
  - build
  - deploy

install-job:
  stage: install
  tags: [docker]
  image: node:20-alpine
  script:
    - npm ci
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/

build-job:
  stage: build
  tags: [docker]
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
      - rag-frontend.tar
  script:
    - docker build -t rag/frontend .
    - docker save rag/frontend -o rag-frontend.tar
  dependencies:
    - install-job

deploy-job:
  stage: deploy
  tags: [docker]
  environment: production
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - rag-frontend.tar
  script:
    - apk add --no-cache sshpass 
    - ls -l
    - sshpass -p "${APP_SERVER_PWD}" scp -o StrictHostKeyChecking=no rag-frontend.tar oslab@${APP_SERVER_IP}:~
    - sshpass -p "${APP_SERVER_PWD}" ssh -o StrictHostKeyChecking=no oslab@${APP_SERVER_IP} '
      if [ "$(docker ps -aq -f name=rag-frontend)" ]; then
      docker stop rag-frontend && docker rm rag-frontend;
      fi;
      docker load -i ~/rag-frontend.tar;
      docker run -d --name rag-frontend -p 80:80 rag/frontend;
      '
  only:
    - master