<template>
  <div class="base-resource-card">
    <div class="resource-icon-wrapper">
      <img :src="node.itemIcon" :alt="node.itemName" class="resource-icon" />
    </div>
    <div class="resource-content">
      <div class="resource-title">{{ node.itemName }}</div>
      <div v-if="node.itemDesc" class="resource-desc">{{ node.itemDesc }}</div>
    </div>
    <button class="insert-btn" title="点击插入到输入框" @click.stop="onInsert">@</button>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import type { CraftingTreeNodeData } from '@/types';
const props = defineProps<{ node: CraftingTreeNodeData }>();
const emit = defineEmits(['select-item']);
function onInsert() {
  emit('select-item', props.node.itemName);
}
</script>

<style scoped>
.base-resource-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #222;
  border: 2px solid #55ff55;
  border-radius: 10px;
  padding: 16px 16px 12px 16px;
  width: 136px !important;
  min-height: 140px;
  max-width: 136px;
  flex-shrink: 0;
  flex-grow: 0;
  justify-content: space-between;
  box-shadow: 0 2px 8px #0008;
}
.resource-icon-wrapper {
  flex-shrink: 0;
}
.resource-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: 1px solid #55ff55;
  background: #fff;
}
.resource-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 8px 0 6px 0;
  text-align: center;
  overflow: hidden;
  width: 100%;
}
.resource-title {
  font-size: 14px;
  font-weight: bold;
  color: #55ff55;
  font-family: 'Minecraft', monospace;
  margin-bottom: 6px;
  text-align: center;
  line-height: 1.2;
}
.resource-desc {
  font-size: 11px;
  color: #ddd;
  line-height: 1.4;
  text-align: center;
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  width: 100%;
}
.insert-btn {
  background: #55ff55;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 16px;
  color: #222;
  cursor: pointer;
  transition: background 0.2s;
  margin-top: 4px;
  flex-shrink: 0;
}
.insert-btn:hover {
  background: #7ee7c7;
}
</style>
