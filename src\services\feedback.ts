import { AxiosResponse } from 'axios';
import httpService from './httpService';

interface FeedbackResponse {
  feedback_id: number;
  success: boolean;
  message: string;
}

/**
   * 提交反馈
   * @param {string} type - 反馈类型
   * @param {string} content - 反馈内容
   * @returns {Promise<AxiosResponse<FeedbackResponse>>}
   */
export const submitFeedback = async (type: string, content: string): Promise<AxiosResponse<FeedbackResponse>> => {
  return await httpService.post<FeedbackResponse>('/feedback', {
    type,
    content
  });
};
