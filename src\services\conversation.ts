import { AxiosResponse } from 'axios';
import httpService from './httpService';
import type { Message, Conversation } from '@/types';

export interface ConversationDetail extends Conversation {
  messages: Message[];
}

// 字段适配（如后端返回 created_at 而不是 createdAt，可在此适配）
function adaptMessage(msg: any): Message {
  // Preserve API fields (created_at) and other properties directly
  return msg;
}

function adaptConversationDetail(data: any): ConversationDetail {
  // Preserve API fields as-is
  return {
    ...data,
    messages: Array.isArray(data.messages) ? data.messages.map(adaptMessage) : []
  };
}

/**
   * 获取所有对话列表
   * @returns {Promise<AxiosResponse<Conversation[]>>}
   */
export const getConversations = async (): Promise<AxiosResponse<Conversation[]>> => {
  return await httpService.get<Conversation[]>('/conversations');
};

/**
   * 获取单个对话详情
   * @param {number} conversationId - 对话ID
   * @returns {Promise<AxiosResponse<ConversationDetail>>}
   */
export const getConversation = async (conversationId: number): Promise<AxiosResponse<ConversationDetail>> => {
  const resp = await httpService.get(`/conversations/${conversationId}`);
  // 适配字段，保证全局类型一致
  return { ...resp, data: adaptConversationDetail(resp.data) };
};

/**
   * 更新对话标题
   * @param {number} conversationId - 对话ID
   * @param {string} title - 新标题
   * @returns {Promise<AxiosResponse<Conversation>>}
   */
export const updateConversationTitle = async (conversationId: number, title: string): Promise<AxiosResponse<Conversation>> => {
  return await httpService.put<Conversation>(`/conversations/${conversationId}`, {
    title
  });
};

/**
   * 删除对话
   * @param {number} conversationId - 对话ID
   * @returns {Promise<AxiosResponse<{ message: string }>>}
   */
export const deleteConversation = async (conversationId: number): Promise<AxiosResponse<{ message: string }>> => {
  return await httpService.delete(`/conversations/${conversationId}`);
};
