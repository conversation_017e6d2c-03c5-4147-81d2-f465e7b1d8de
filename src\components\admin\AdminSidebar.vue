<template>
  <nav class="admin-sidebar">
    <AdminUpload :is-visible="showUpload" @close="showUpload = false" @upload="handleUpload" />
    <div class="sidebar-section">
      <button class="sidebar-btn" :class="{ active: props.currentType === 'NEW' }" title="新上传的" @click="handleTypeClick('NEW')">
        <span class="sidebar-icon material-icons">cloud_upload</span>
        新上传的
      </button>
      <button class="sidebar-btn" :class="{ active: props.currentType === 'POST' }" title="教程" @click="handleTypeClick('POST')">
        <span class="sidebar-icon material-icons">menu_book</span>
        教程
      </button>
      <button class="sidebar-btn" :class="{ active: props.currentType === 'BBS' }" title="讨论" @click="handleTypeClick('BBS')">
        <span class="sidebar-icon material-icons">forum</span>
        讨论
      </button>
      <button class="sidebar-btn" :class="{ active: props.currentType === 'WIKI' }" title="原版内容" @click="handleTypeClick('WIKI')">
        <span class="sidebar-icon material-icons">extension</span>
        原版内容
      </button>
      <button class="sidebar-btn" :class="{ active: props.currentType === 'MODE' }" title="模组" @click="handleTypeClick('MODE')">
        <span class="sidebar-icon material-icons">widgets</span>
        模组
      </button>
      <button class="sidebar-btn" :class="{ active: props.currentType === 'ITEM' }" title="物品" @click="handleTypeClick('ITEM')">
        <span class="sidebar-icon material-icons">inventory_2</span>
        物品
      </button>
    </div>
    <div class="sidebar-bottom">
      <div class="sidebar-divider"></div>
      <button class="sidebar-btn upload-btn" @click="showUpload = true">
        <span class="sidebar-icon material-icons">upload_file</span>
        上传知识库
      </button>
      <button class="sidebar-btn home-btn" @click="$router.push('/')">
        对话界面
      </button>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import AdminUpload from './AdminUpload.vue';
const props = defineProps<{ currentType: 'WIKI' | 'POST' | 'BBS' | 'MODE' | 'ITEM' | 'NEW' }>();
const emit = defineEmits<{
  (e: 'update:type', type: 'WIKI' | 'POST' | 'BBS' | 'MODE' | 'ITEM' | 'NEW'): void;
  (e: 'upload', dataArr: { file: File, metadata: string[] }[]): void;
}>();

function handleTypeClick(type: 'WIKI' | 'POST' | 'BBS' | 'MODE' | 'ITEM' | 'NEW') {
  emit('update:type', type);
}

const showUpload = ref(false);
function handleUpload(dataArr: { file: File, metadata: string[] }[]) {
  emit('upload', dataArr);
}
</script>

<style scoped lang="scss">
.admin-sidebar {
  width: 230px;
  background: linear-gradient(180deg, #23272a 80%, #1a1d1f 100%);
  display: flex;
  flex-direction: column;
  padding: 32px 0 0 0;
  align-items: stretch;
  min-height: 0;
  box-shadow: 2px 0 8px rgba(0,0,0,0.10);
  position: relative;
  user-select: none;
}
.sidebar-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.sidebar-btn {
  background: none;
  border: none;
  color: #55ff55;
  font-family: 'Minecraft', monospace;
  font-size: 1.1rem;
  padding: 14px 14px 14px 14px;
  margin: 0 18px 8px 18px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  box-shadow: 0 0 0 #55ff5500;
  position: relative;
  user-select: none;
}
.sidebar-btn .sidebar-icon {
  font-size: 20px;
  color: #7dff7d;
  filter: drop-shadow(0 0 2px #55ff55cc);
  user-select: none;
}
.sidebar-btn:hover {
  background: #232;
  color: #aaffaa;
  box-shadow: 0 0 8px #55ff55cc, 0 2px 8px #0004;
  /* transform: translateX(6px) scale(1.04); */
  z-index: 2;
}
.sidebar-btn:active {
  background: #111;
  color: #55ff55;
}
.sidebar-bottom {
  margin-top: auto;
  padding-bottom: 32px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.sidebar-divider {
  height: 1.5px;
  background: linear-gradient(90deg, #55ff55 0%, #23272a 100%);
  margin: 0 18px 18px 18px;
  border-radius: 1px;
  opacity: 0.5;
}
.upload-btn {
  background: #b8860b;
  color: #fff8e1;
  border: 2px solid #b8860b;
  font-weight: bold;
  margin: 0 18px 14px 18px;
  box-shadow: 0 2px 12px #b8860b55, 0 2px 8px #0004;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  font-size: 16px;
  padding: 10px 0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}
.upload-btn .sidebar-icon {
  color: #fff8e1;
  filter: drop-shadow(0 0 2px #b8860bcc);
}
.upload-btn:hover {
  background: #8d6e00;
  color: #fffde7;
  box-shadow: 0 0 16px #b8860bcc, 0 2px 8px #0004;
}
.home-btn {
  background: #2c2c2c;
  color: #55ff55;
  border: 2px solid #55ff55;
  font-size: 16px;
  font-family: 'Minecraft', monospace;
  font-weight: bold;
  padding: 10px 0;
  border-radius: 8px;
  margin: 0 18px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  display: flex;
  align-items: center;
  gap: 12px;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  justify-content: center;
  text-align: center;
  user-select: none;
}
.home-btn:hover {
  background: #55ff55;
  color: #23272a;
  /* 不发光 */
  box-shadow: none;
  /* transform: none; */
}
.sidebar-btn.active {
  background: #232;
  color: #aaffaa;
  box-shadow: 0 0 8px #55ff55cc, 0 2px 8px #0004;
  /* transform: none; */
  z-index: 2;
}
</style>
