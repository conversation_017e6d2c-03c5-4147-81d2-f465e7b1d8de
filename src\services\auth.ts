import { AxiosResponse } from 'axios';
import httpService from './httpService';

interface AuthResponse {
  access_token: string;
  token_type: string;
}

interface BasicResponse {
  success: boolean;
  message: string;
}

// 登录
export const login = async (username: string, password: string): Promise<AxiosResponse<AuthResponse>> => {
  return await httpService.post<AuthResponse>('/auth/token', new URLSearchParams({
    username,
    password,
    grant_type: 'password'
  }), {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });
};

// 注册
export const register = async (username: string, email: string, password: string): Promise<AxiosResponse<BasicResponse>> => {
  return await httpService.post<BasicResponse>('/auth/register', {
    username,
    email,
    password
  });
};

// 管理员注册
export const adminRegister = async (username: string, email: string, password: string, verification: string): Promise<AxiosResponse<BasicResponse>> => {
  return await httpService.post<BasicResponse>('/auth/adminRegister', {
    username,
    email,
    password,
    verification
  });
};
