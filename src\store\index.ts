import { createStore } from 'vuex';
import {
  getConversations,
  getConversation,
  updateConversationTitle,
  deleteConversation
} from '@/services/conversation';
import { login, register, adminRegister } from '@/services/auth';
import { getCurrentUser } from '@/services/user';
import { submitFeedback } from '@/services/feedback';
import type { Message, Conversation, UserData, ConversationCraftingContext } from '@/types';
import { startNewConversationSSE, addMessageToConversationSSE } from '@/services/conversationSSE';

export default createStore({
  state: {
    token: localStorage.getItem('token'),
    user: null as UserData | null,
    isAuthenticated: !!localStorage.getItem('token'),
    error: null,
    loading: false,
    conversations: [] as Conversation[],
    currentConversation: null as Conversation | null,
    loadingConversations: false,
    loadingMessages: false,
    feedbackSubmitting: false, // 添加反馈提交状态
    currentAbortController: null as AbortController | null, // 用于取消流式传输
    isAdmin: localStorage.getItem('role') === 'ADMIN', // 由role判断
    showModeSelectionModal: false, // 新增：模式选择弹窗状态
    // 新增：合成导航上下文管理
    currentCraftingContext: null as import('@/types').ConversationCraftingContext | null,
    // 新增：下一个对话的模式（用于延迟创建）
    nextConversationMode: null as 'NORMAL' | 'SYNTHETIC_NAVIGATION' | null
  },
  getters: {
    isAuthenticated: (state) => state.isAuthenticated,
    currentUser: (state) => state.user,
    authError: (state) => state.error,
    allConversations: (state) => state.conversations,
    currentConversation: (state) => state.currentConversation,
    isAdmin: (state) => state.isAdmin, // getter不变
    // 新增：合成上下文相关 getters
    currentCraftingContext: (state) => state.currentCraftingContext,
    isInCraftingMode: (state) => state.currentConversation?.type === 'SYNTHETIC_NAVIGATION',
    // 新增：是否在等待创建特定模式的对话
    isWaitingForModeInput: (state) => state.nextConversationMode !== null && state.currentConversation === null
  },
  mutations: {
    setToken(state, token) {
      state.token = token;
      state.isAuthenticated = !!token;
      if (token) {
        localStorage.setItem('token', token);
      } else {
        localStorage.removeItem('token');
      }
    },
    setUser(state, user) {
      state.user = user;
      // 新增：同步 isAdmin
      const isAdmin = user && (user.role === 'ADMIN');
      state.isAdmin = isAdmin;
      localStorage.setItem('role', user?.role || ''); // 存储role
    },
    setError(state, error) {
      state.error = error;
    },
    setLoading(state, status) {
      state.loading = status;
    },
    clearAuthState(state) {
      state.token = null;
      state.user = null;
      state.isAuthenticated = false;
      state.isAdmin = false; // 新增
      localStorage.removeItem('token');
      localStorage.removeItem('role'); // 移除role
    },
    setConversations(state, conversations) {
      state.conversations = conversations;
    }, setCurrentConversation(state, conversation) {
      // 检查当前会话是否有正在加载的消息
      const isLoading = !!state.currentConversation?.messages?.find(m => m.isLoading);
      if (isLoading && state.currentConversation) {
        console.log('当前有消息正在生成，保留当前会话状态');
        return;
      }
      // 如果是同一个会话，合并消息而不是替换
      if (state.currentConversation && conversation && state.currentConversation.id === conversation.id) {
        if (conversation.messages) {
          state.currentConversation.messages = conversation.messages;
          // 同一会话更新时也需要同步合成树数据
          if (conversation.type === 'SYNTHETIC_NAVIGATION' && conversation.craftingContext) {
            const craftingTree = conversation.craftingContext.sharedTree;
            conversation.messages.forEach((message: import('@/types').Message) => {
              if (message.type === 'CRAFTING' && !message.craftingData) {
                message.craftingData = craftingTree;
                console.log(`[Store] 为同一会话的消息 ${message.id} 添加了craftingData`);
              }
            });
          }
        }
        console.log('更新了同一会话的消息');
      } else {
        state.currentConversation = conversation;
        console.log('设置了新的当前会话:', conversation?.id);
        // 同步合成上下文
        if (conversation?.type === 'SYNTHETIC_NAVIGATION' && conversation.craftingContext) {
          state.currentCraftingContext = conversation.craftingContext;
        } else {
          state.currentCraftingContext = null;
        }
      }

      // 关键修复：同步合成树数据到CRAFTING类型的消息中
      if (conversation && conversation.type === 'SYNTHETIC_NAVIGATION' && conversation.craftingContext && conversation.messages) {
        const craftingTree = conversation.craftingContext.sharedTree;
        console.log('[Store] 同步合成树数据到CRAFTING消息中:', craftingTree);

        // 遍历所有消息，为CRAFTING类型的消息添加craftingData
        conversation.messages.forEach((message: import('@/types').Message) => {
          if (message.type === 'CRAFTING' && !message.craftingData) {
            message.craftingData = craftingTree;
            console.log(`[Store] 为消息 ${message.id} 添加了craftingData`);
          }
        });
      }
    },
    addConversation(state, conversation) {
      state.conversations.unshift(conversation); // 添加到队列前面
    }, updateConversation(state, { id, title, type, craftingContext }) {
      const index = state.conversations.findIndex(c => c.id === id);
      if (index !== -1) {
        if (title !== undefined) {
          state.conversations[index].title = title;
        }
        if (type !== undefined) {
          state.conversations[index].type = type;
        }
        if (craftingContext !== undefined) {
          state.conversations[index].craftingContext = craftingContext;
        }
        if (state.currentConversation && state.currentConversation.id === id) {
          if (title !== undefined) {
            state.currentConversation.title = title;
          }
          if (type !== undefined) {
            state.currentConversation.type = type;
          }
          if (craftingContext !== undefined) {
            state.currentConversation.craftingContext = craftingContext;
          }
        }
      }
    },

    // 更新会话列表中的会话信息
    updateConversationInList(state, { id, title }) {
      // 查找会话在列表中的索引
      const index = state.conversations.findIndex(c => c.id === id);

      if (index !== -1) {
        // 如果找到了会话，更新它的标题
        state.conversations[index].title = title;
        // 更新时间戳
        state.conversations[index].updated_at = new Date().toISOString();

        // 如果这个会话不在列表的第一位，将它移到第一位
        if (index > 0) {
          // 移除会话
          const conversation = state.conversations.splice(index, 1)[0];
          // 添加到列表开头
          state.conversations.unshift(conversation);
        }

        // 同时更新当前会话的标题（如果是当前会话）
        if (state.currentConversation && state.currentConversation.id === id) {
          state.currentConversation.title = title;
        }
      } else {
        // 如果会话不在列表中，可能需要添加它
        // 但通常这种情况不会发生，因为会话应该已经在列表中
        console.log('会话不在列表中，无法更新标题:', id);
      }
    },
    deleteConversationById(state, id) {
      state.conversations = state.conversations.filter(c => c.id !== id);
      if (state.currentConversation && state.currentConversation.id === id) {
        state.currentConversation = null;
      }
    },
    addMessagesToConversation(state, { conversationId, messages }) {
      if (state.currentConversation && state.currentConversation.id === conversationId) {
        if (!state.currentConversation.messages) {
          state.currentConversation.messages = [];
        }

        // Clone the existing messages to create a new array reference
        const updatedMessages = [...state.currentConversation.messages];
        updatedMessages.push(...messages);

        // Replace the entire messages array with the new one
        state.currentConversation.messages = updatedMessages;
      }
    },
    setLoadingConversations(state, status) {
      state.loadingConversations = status;
    },
    setLoadingMessages(state, status) {
      state.loadingMessages = status;
    },
    setFeedbackSubmitting(state, status) {
      state.feedbackSubmitting = status;
    }, clearConversations(state) {
      state.conversations = [];
      state.currentConversation = null;
    },
    // 新增：设置当前的 AbortController
    setCurrentAbortController(state, controller) {
      state.currentAbortController = controller;
    },    // 新增：模式选择弹窗控制
    setShowModeSelectionModal(state, show) {
      state.showModeSelectionModal = show;
    },
    // 新增：合成上下文管理
    setCurrentCraftingContext(state, context) {
      state.currentCraftingContext = context;
    },
    clearCurrentCraftingContext(state) {
      state.currentCraftingContext = null;
    },
    // 新增：向消息追加内容块
    appendMessageChunk(state, { messageId, chunk }) {
      if (state.currentConversation && state.currentConversation.messages) {
        const message = state.currentConversation.messages.find(m => m.id === messageId);
        if (message) {
          // 创建新的内容，确保响应式更新
          if (chunk) {
            message.content = (message.content || '') + chunk;
          } else {
            message.content = (message.content || '') + '\n';
          }
        }
      }
    },
    // 新增：完成助手消息（停止加载状态）
    finalizeAssistantMessage(state, { messageId, error = false, content = null }) {
      if (state.currentConversation && state.currentConversation.messages) {
        const message = state.currentConversation.messages.find(m => m.id === messageId);
        if (message) {
          message.isLoading = false;
          message.isError = error;
          if (content !== null) {
            message.content = content;
          }
        }
      }
    },
    // setConversationSources mutation 已移除
    // 更新新对话的详细信息（从临时ID转换为真实ID）
    updateNewConversationDetails(state, { tempId, conversation_id, title }) {
      // 更新当前对话
      if (state.currentConversation) {
        // 保存当前对话的消息和其他属性
        const currentMessages = state.currentConversation.messages || [];

        // 查找临时ID在会话列表中的索引
        const tempIndex = state.conversations.findIndex(c =>
          String(c.id).startsWith('temp_')
        );

        // 如果找到了临时ID的会话，从列表中移除它
        if (tempIndex !== -1) {
          state.conversations.splice(tempIndex, 1);
        }

        // 更新当前会话的ID和标题
        state.currentConversation.id = conversation_id;
        state.currentConversation.title = title;

        // 检查真实ID是否已存在于会话列表中
        const existingIndex = state.conversations.findIndex(c => c.id === conversation_id);

        if (existingIndex === -1) {
          // 如果会话列表中没有这个真实ID的会话，添加它
          state.conversations.unshift({
            id: conversation_id,
            title: title,
            type: state.currentConversation.type, // 修复：补全 type 字段
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            messages: currentMessages // 保留消息
          });
        } else {
          // 如果已存在，更新它的标题和消息
          state.conversations[existingIndex].title = title;
          state.conversations[existingIndex].messages = currentMessages;
        }

        console.log(`对话ID已更新: 临时ID -> ${conversation_id}`);
      }
    },
    // 新增：开始助手流式加载消息
    startLoadingAssistantMessage(state, { conversationId }) {
      const conv = state.currentConversation;
      if (!conv || conv.id !== conversationId) return;
      const loadingMsg: Message = {
        id: `loading_${Date.now()}`,
        role: 'assistant',
        type: 'CRAFTING',
        content: '',
        isLoading: true,
        created_at: new Date().toISOString()
      };
      conv.messages = conv.messages || [];
      conv.messages.push(loadingMsg);
    },
    // 新增：追加助手流式加载内容
    appendLoadingMessageChunk(state, { conversationId, chunk }) {
      const conv = state.currentConversation;
      if (!conv || conv.id !== conversationId || !conv.messages) return;
      for (let i = conv.messages.length - 1; i >= 0; i--) {
        const msg = conv.messages[i]!;
        if (msg.isLoading) {
          msg.content += chunk;
          break;
        }
      }
    },
    // 修改：停止助手流式加载，设置 isLoading = false，并更新最终类型和物品名称
    finalizeLoadingMessage(state, { conversationId, finalType = 'TEXT', finalItemName = null }: { conversationId: string | number; finalType?: 'TEXT' | 'CRAFTING'; finalItemName?: string | null }) { // <-- Fix finalType type
      const conv = state.currentConversation;
      if (!conv || conv.id !== conversationId || !conv.messages) return;
      for (let i = conv.messages.length - 1; i >= 0; i--) {
        const msg = conv.messages[i]!;
        if (msg.isLoading) {
          msg.isLoading = false;
          msg.type = finalType; // 更新消息类型
          msg.item_name = finalItemName === null ? undefined : finalItemName; // <-- Assign undefined if null
          msg.isError = false; // 确保错误状态为 false
          console.log(`[store] Finalized message ${msg.id} with type: ${finalType}, item: ${finalItemName}`);

          // 强制DOM重新渲染以修复字体显示问题
          setTimeout(async () => {
            const { delayedFontRefresh } = await import('@/utils/fontUtils');
            delayedFontRefresh(50);
          }, 100);

          break;
        }
      }
    },

    // 新增：处理加载错误
    finalizeLoadingMessageWithError(state, { conversationId, errorMessage }: { conversationId: string | number; errorMessage: string }) {
        const conv = state.currentConversation;
        if (!conv || conv.id !== conversationId || !conv.messages) return;
        for (let i = conv.messages.length - 1; i >= 0; i--) {
            const msg = conv.messages[i]!;
            if (msg.isLoading) {
                msg.isLoading = false;
                msg.isError = true; // 标记为错误
                msg.content = errorMessage; // 显示错误信息
                console.error(`[store] Finalized message ${msg.id} with error: ${errorMessage}`);
                break;
            }
        }
    },

    // 新增：更新合成上下文
    updateCraftingContext(state, { conversationId, context }) {
      const conv = state.conversations.find(c => c.id === conversationId);
      if (conv) conv.craftingContext = context;
      if (state.currentConversation && state.currentConversation.id === conversationId) {
        state.currentConversation.craftingContext = context;
        state.currentCraftingContext = context;
      }
    },
    // 新增：将加载占位消息替换为 crafting 卡片消息，并附带合成树
    replaceLoadingWithCraftingCard(state, { conversationId, context }: { conversationId: string | number; context: import('@/types').ConversationCraftingContext }) {
      const conv = state.currentConversation;
      if (!conv || conv.id !== conversationId || !conv.messages) return;
      for (let i = conv.messages.length - 1; i >= 0; i--) {
        const msg = conv.messages[i]!;
        if (msg.isLoading) {
          msg.isLoading = false;
          msg.type = 'CRAFTING';
          msg.craftingData = context.sharedTree;
          msg.content = '';
          break;
        }
      }
    },

    // 新增：设置下一个对话的模式
    setNextConversationMode(state, mode: 'NORMAL' | 'SYNTHETIC_NAVIGATION' | null) {
      state.nextConversationMode = mode;
    }
  },
  actions: {
    async login({ commit, dispatch }, { username, password }) {
      try {
        commit('setLoading', true);
        commit('setError', null);
        const response = await login(username, password);
        const token = response.data.access_token;
        commit('setToken', token);
        await dispatch('fetchCurrentUser');
        return true;
      } catch (error: any) {
        let errorMsg = '登录失败';
        if (error.response) {
          errorMsg = error.response.data.detail || '用户名或密码错误';
        }
        commit('setError', errorMsg);
        return false;
      } finally {
        commit('setLoading', false);
        console.log('用户身份:', localStorage.getItem('role'));
      }
    },
    async register({ commit }, { username, email, password }) {
      try {
        commit('setLoading', true);
        commit('setError', null);
        await register(username, email, password);
        return true;
      } catch (error: any) {
        let errorMsg = '注册失败';
        if (error.response) {
          errorMsg = error.response.data.detail || '注册信息有误';
        }
        commit('setError', errorMsg);
        return false;
      } finally {
        commit('setLoading', false);
      }
    },
    async adminRegister({ commit }, { username, email, password, verification }) {
      try {
        commit('setLoading', true);
        commit('setError', null);
        await adminRegister(username, email, password, verification);
        return true;
      } catch (error: any) {
        let errorMsg = '注册失败';
        if (error.response) {
          errorMsg = error.response.data.detail || '注册信息有误';
        }
        commit('setError', errorMsg);
        return false;
      } finally {
        commit('setLoading', false);
      }
    },
    async fetchCurrentUser({ commit }) {
      try {
        commit('setLoading', true);
        const userResponse = await getCurrentUser();
        commit('setUser', userResponse.data);
        return userResponse.data;
      } catch (error) {
        commit('clearAuthState');
        return null;
      } finally {
        commit('setLoading', false);
      }
    },
    logout({ commit }) {
      commit('clearAuthState');
      commit('clearConversations');
    },

    // 会话相关的actions
    async fetchConversations({ commit }) {
      try {
        commit('setLoadingConversations', true);
        const convListResponse = await getConversations();
        commit('setConversations', convListResponse.data);
        return convListResponse.data;
      } catch (error) {
        console.error('获取会话列表失败:', error);
        return [];
      } finally {
        commit('setLoadingConversations', false);
      }
    },
    async fetchConversationDetail({ commit, state }, conversationId) {
      try {
        // 检查当前会话是否有正在加载的消息
        const isLoading = !!state.currentConversation?.messages?.find(m => m.isLoading);
        if (isLoading) {
          console.log('跳过获取会话详情，因为当前有消息正在生成:', conversationId);
          return state.currentConversation;
        }
        // 检查是否已经是当前会话
        if (state.currentConversation?.id === conversationId) {
          console.log('当前会话已经是请求的会话，跳过重复加载:', conversationId);
          return state.currentConversation;
        }
        console.log('获取会话详情:', conversationId);
        commit('setLoadingMessages', true);
        const convResponse = await getConversation(conversationId);
        // 设置新的会话
        commit('setCurrentConversation', convResponse.data);
        return convResponse.data;
      } catch (error) {
        console.error('获取会话详情失败:', error);
        return null;
      } finally {
        commit('setLoadingMessages', false);
      }
    },
    async createNewConversation({ commit, state }, { message, type = 'NORMAL' }) {
      if (state.currentAbortController) {
        state.currentAbortController.abort();
        commit('setCurrentAbortController', null);
      }
      try {
        commit('setLoadingMessages', true);
        const tempConversationId = `temp_${Date.now()}`;
        const userMessage = {
          id: `user_${Date.now()}`,
          role: 'user',
          type,
          content: message,
          created_at: new Date().toISOString(),
          isLoading: false
        };
        const tempAssistantId = `temp_${Date.now() + 1}`;
        const assistantPlaceholder = {
          id: tempAssistantId,
          role: 'assistant',
          type,
          content: '',
          created_at: new Date().toISOString(),
          isLoading: true,
          isError: false
        };
        const tempConversation = {
          id: tempConversationId,
          title: '新对话',
          type,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          messages: [userMessage, assistantPlaceholder]
        };
        commit('addConversation', tempConversation);
        commit('setCurrentConversation', tempConversation);
        const ctrl = startNewConversationSSE({
          message,
          type,
          token: state.token as string,
          callbacks: {
            onOpen: () => { },
            onMessageChunk: (chunk) => {
              commit('appendMessageChunk', { messageId: tempAssistantId, chunk });
            },
            onEnd: () => {
              commit('finalizeAssistantMessage', { messageId: tempAssistantId });
              commit('setCurrentAbortController', null);
            },
            onError: (msg) => {
              commit('finalizeAssistantMessage', {
                messageId: tempAssistantId,
                error: true,
                content: msg
              });
              commit('setCurrentAbortController', null);
            },
            onConversationStart: (startData) => {
              commit('updateNewConversationDetails', {
                tempId: tempAssistantId,
                conversation_id: startData.conversationId,
                title: startData.title || '新对话'
              });
              if (startData.conversationId && startData.title) {
                commit('updateConversationInList', {
                  id: startData.conversationId,
                  title: startData.title
                });
              }
            },
            onTitleUpdate: (title) => {
              if (state.currentConversation) {
                commit('updateConversation', {
                  id: state.currentConversation.id,
                  title
                });
              }
            },
            onCraftingContext: (ctx: ConversationCraftingContext) => {
              const loadingMsg = state.currentConversation?.messages?.find(m => m.isLoading && m.role === 'assistant');
              if (loadingMsg && state.currentConversation) {
                const conversationId = state.currentConversation.id; // 可能是临时ID或真实ID
                console.log(`[store] Received crafting_context for conversation ${conversationId}, message ${loadingMsg.id}`);
                commit('replaceLoadingWithCraftingCard', { conversationId: conversationId, context: ctx });
                commit('updateCraftingContext', { conversationId: conversationId, context: ctx });
              } else {
                console.warn('[store] Received crafting_context but could not find loading assistant message in current conversation.');
              }
            },
            onMessageComplete: (data) => {
              // 处理 message_complete 事件，补全 type/item_name
              const assistantMsg = state.currentConversation?.messages?.find(m => m.id === tempAssistantId || (m.role === 'assistant' && !m.isLoading)); // 查找临时ID或最后一个非加载助手消息
                if (assistantMsg && state.currentConversation) {
                  const conversationId = state.currentConversation.id;
                  // 如果是合成消息完成，确保类型正确
                  // 注意：后端 SSE 事件名是 'crafting_context'，但消息类型可能是 'CRAFTING' (大写)
                  if (data.type && data.type === 'CRAFTING' && assistantMsg.type !== 'CRAFTING') {
                    console.log(`[store] Updating message type to CRAFTING via onMessageComplete for message ${assistantMsg.id}`);
                    assistantMsg.type = 'CRAFTING';
                    // 如果 craftingData 没有通过 onCraftingContext 传递，可以在这里处理
                    // if (!assistantMsg.craftingData && data.crafting_tree) {
                    //   assistantMsg.craftingData = data.crafting_tree;
                    // }
                  }
                  // 如果需要，可以在这里更新 item_name
                  // if (data.item_name) {
                  //   assistantMsg.itemName = data.item_name;
                  // }
                }
            }
          }
        });
        commit('setCurrentAbortController', ctrl);
        return state.currentConversation;
      } catch (error) {
        console.error('创建新会话失败:', error);
        return null;
      } finally {
        commit('setLoadingMessages', false);
      }
    },
    async addMessageToConversation({ commit, state }, { conversationId, message }) {
      if (state.currentAbortController) {
        state.currentAbortController.abort();
        commit('setCurrentAbortController', null);
      }
      try {
        commit('setLoadingMessages', true);
        const userMessage = {
          id: `user_${Date.now()}`,
          role: 'user',
          type: 'TEXT',
          content: message,
          created_at: new Date().toISOString(),
          isLoading: false
        };
        commit('addMessagesToConversation', {
          conversationId,
          messages: [userMessage]
        });
        const tempAssistantId = `temp_${Date.now()}`;
        const assistantPlaceholder = {
          id: tempAssistantId,
          role: 'assistant',
          type: 'TEXT',
          content: '',
          created_at: new Date().toISOString(),
          isLoading: true,
          isError: false
        };
        commit('addMessagesToConversation', {
          conversationId,
          messages: [assistantPlaceholder]        });        const ctrl = addMessageToConversationSSE({
          conversationId: String(conversationId),
          message,
          token: state.token as string,
          callbacks: {
            onOpen: () => { },
            onMessageChunk: (chunk: string) => {
              commit('appendMessageChunk', { messageId: tempAssistantId, chunk });
            },
            onEnd: () => {
              commit('finalizeAssistantMessage', { messageId: tempAssistantId });
              commit('setCurrentAbortController', null);
            },
            onError: (msg: string) => {
              commit('finalizeAssistantMessage', {
                messageId: tempAssistantId,
                error: true,
                content: msg
              });
              commit('setCurrentAbortController', null);
            },
            onMessageComplete: (data: any) => {
              if (data.type && data.type === 'crafting' && data.item_name) {
                // 可在此处处理合成卡片消息
              }
            }
          }
        });
        commit('setCurrentAbortController', ctrl);
        // 更新时间戳
        const conversationIndex = state.conversations.findIndex((c: any) => c.id === conversationId);
        if (conversationIndex !== -1) {
          const updatedConversation = { ...state.conversations[conversationIndex] };
          updatedConversation.updated_at = new Date().toISOString();
          const newConversations = state.conversations.filter((c: any) => c.id !== conversationId);
          newConversations.unshift(updatedConversation);
          commit('setConversations', newConversations);
        } return true;
      } catch (error) {
        console.error('发送消息失败:', error);
        return false;
      } finally {
        commit('setLoadingMessages', false);
      }
    },

    async updateConversationTitle({ commit }, { conversationId, title }) {
      try {
        const updateTitleResponse = await updateConversationTitle(conversationId, title);
        commit('updateConversation', { id: conversationId, title });
        return updateTitleResponse.data;
      } catch (error) {
        console.error('更新会话标题失败:', error);
        return null;
      }
    },

    async deleteConversation({ commit }, conversationId) {
      try {
        await deleteConversation(conversationId);
        commit('deleteConversationById', conversationId);
        return true;
      } catch (error) {
        console.error('删除会话失败:', error);
        return false;
      }
    },

    async submitFeedback({ commit }, { type, content }) {
      try {
        commit('setFeedbackSubmitting', true);
        commit('setError', null);
        const feedbackResponse = await submitFeedback(type, content);
        return true;
      } catch (error) {
        let errorMsg = '提交反馈失败';
        if ((error as any).response) {
          errorMsg = (error as any).response.data.detail || '提交失败，请稍后再试';
        }
        commit('setError', errorMsg);
        return false;
      } finally {
        commit('setFeedbackSubmitting', false);
      }
    },

    // 新增：取消流式传输
    cancelStream({ state, commit }) {
      if (state.currentAbortController) {
        console.log("用户请求取消流...");
        state.currentAbortController.abort();

        // 查找正在加载的消息并标记为已取消
        if (state.currentConversation && state.currentConversation.messages) {
          const loadingMessage = state.currentConversation.messages.find(m => m.isLoading);
          if (loadingMessage) {
            commit('finalizeAssistantMessage', {
              messageId: loadingMessage.id,
              content: (loadingMessage.content || "") + " (已停止)"
            });
          }
        }

        commit('setCurrentAbortController', null);
      }
    },

    // 新增：显示模式选择弹窗
    showModeSelectionModal({ commit }) {
      commit('setShowModeSelectionModal', true);
    },

    // 新增：隐藏模式选择弹窗
    hideModeSelectionModal({ commit }) {
      commit('setShowModeSelectionModal', false);
    },

    // 新增：根据模式创建会话
    async createConversationWithMode({ commit, dispatch }, { mode, message = '' }: { mode: 'normal' | 'crafting'; message?: string }) {
      try {
        commit('setShowModeSelectionModal', false);

        if (message) {
          // 如果有消息，立即创建对话
          const type = mode === 'crafting' ? 'SYNTHETIC_NAVIGATION' : 'NORMAL';
          await dispatch('createNewConversation', { message, type });
        } else {
          // 如果没有消息，设置下一个对话的模式，等待用户输入
          const type = mode === 'crafting' ? 'SYNTHETIC_NAVIGATION' : 'NORMAL';
          commit('setNextConversationMode', type);
          // 清空当前对话，让界面准备接收新对话
          commit('setCurrentConversation', null);
        }
      } catch (error) {
        console.error('根据模式创建会话失败:', error);
      }
    },    // 新增：发送合成导航查询（SSE模式）
    sendCraftingQuery({ state, commit }, payload: { query: string }) {
      const { query } = payload;
      const conv = state.currentConversation;
      if (!conv) return;
      const token = state.token!;

      // 添加用户消息
      const userMsg: Message = {
        id: `user_${Date.now()}`,
        role: 'user',
        type: 'TEXT',
        content: query,
        created_at: new Date().toISOString(),
        isLoading: false
      };
      commit('addMessagesToConversation', { conversationId: conv.id, messages: [userMsg] });

      // 添加加载占位助手消息
      commit('startLoadingAssistantMessage', { conversationId: conv.id });

      const callbacks = {
        onOpen: () => { },
        onCraftingContext: (ctx: ConversationCraftingContext) => commit('updateCraftingContext', { conversationId: conv.id, context: ctx }),
        onMessageChunk: (chunk: string) => commit('appendLoadingMessageChunk', { conversationId: conv.id, chunk }),
        onEnd: () => commit('finalizeLoadingMessage', { conversationId: conv.id }),
        onError: (msg: string) => console.error(msg)
      };

      if (!conv.craftingContext) {
        // 首次查询
        startNewConversationSSE({ message: query, type: 'SYNTHETIC_NAVIGATION', token, callbacks });      } else {        // 后续查询
        addMessageToConversationSSE({ conversationId: String(conv.id), message: query, token, callbacks });
      }
    }
  },
  modules: {
  }
});
