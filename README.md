# Minecraft 知识助手 - RAG聊天应用

## 项目简介

Minecraft知识助手是一个基于Vue 3和TypeScript构建的检索增强生成(RAG)聊天应用，采用Minecraft风格设计，专为游戏爱好者提供游戏相关知识问答服务。该应用结合了现代聊天界面与经典像素游戏美学，打造沉浸式问答体验。

## ✨ 特色功能

### 🎮 Minecraft风格界面
- **像素艺术设计**：完美还原Minecraft独特的像素风格
- **游戏字体集成**：使用官方Minecraft字体，支持中英文双语显示
- **游戏元素融入**：聊天界面融入爬行者、村民等游戏元素
- **暗色主题**：采用游戏内风格的暗色系界面，降低眼睛疲劳

### 💬 智能对话系统
- **多会话管理**：支持创建、切换和删除不同的聊天会话
- **历史记录保存**：自动本地存储所有对话历史
- **会话自动命名**：根据首条用户消息智能生成会话标题
- **打字动画**：回答加载时显示动态加载动画

### 🔍 Minecraft知识库
- **游戏知识检索**：内置Minecraft核心知识，包括：
  - 物品合成配方
  - 生物特性与行为
  - 生态群系介绍
  - 附魔系统详解
- **关键词识别**：智能识别问题中的关键词提供精准回答

### 🔧 强大的用户体验
- **ChatGPT风格侧边栏**：可折叠式历史记录侧边栏
- **键盘快捷键**：支持Enter发送消息
- **响应式设计**：自适应各种屏幕尺寸
- **细节交互优化**：平滑过渡、消息时间戳等人性化设计

## 🛠️ 技术实现

- **框架**: Vue 3 + TypeScript
- **状态管理**: Vuex
- **路由**: Vue Router
- **网络请求**: Axios
- **样式**: SCSS with scoped styling
- **本地存储**: localStorage
- **构建工具**: Vite

## 📁 项目结构

```
src/
├── assets/             # 静态资源(Minecraft风格图片和字体)
├── components/         # Vue组件
│   └── ChatInterface.vue  # 主聊天界面组件
├── services/           # API服务
│   └── api.ts          # 后端API交互服务
├── store/              # Vuex状态管理
├── views/              # 视图组件
│   └── ChatView.vue    # 主视图(含侧边栏和聊天界面)
├── App.vue             # 应用根组件
├── main.ts             # 应用入口
└── router/             # 路由配置
```

## 🚀 快速开始

### 前提条件
- Node.js (v16+)
- npm 或 yarn

### 安装步骤

1. **克隆仓库**
```bash
git clone https://github.com/yourusername/minecraft-rag-frontend.git
cd minecraft-rag-frontend
```

2. **安装依赖**
```bash
npm install
# 或
yarn install
```

3. **运行开发服务器**
```bash
npm run dev
# 或
yarn dev
```

## 🌟 项目亮点

1. **游戏风格完美结合AI**：将现代RAG技术与经典游戏美学无缝融合
2. **中英文双语字体优化**：针对不同字符集使用专门优化的像素字体
3. **专注游戏领域知识**：针对Minecraft玩家提供精准、有用的游戏信息
4. **流畅用户体验**：从背景到消息加载动画，处处体现精心设计

## 🔮 TODO

**迭代三**

- [ ] 多轮对话
- [ ] 添加用户账户系统
- [ ] 等待回答时进度条展示/流式传输