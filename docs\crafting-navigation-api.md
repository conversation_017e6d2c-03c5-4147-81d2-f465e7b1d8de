# 合成导航模式 API 接口详细说明

本文档定义了“合成导航”模式下前后端交互的所有接口、数据结构和 SSE 事件格式，供后端开发和前端联调参考。

---

## 一、概述

合成导航模式是一种特殊的对话模式，用户在创建对话时选择该模式后，系统会为该对话维护一个**会话级合成树**。核心流程如下：

1. **对话创建**：用户选择"合成导航"对话类型
2. **首条消息处理**：用户发送包含物品名称的消息，系统识别物品实体并生成合成树
3. **后续消息处理**：系统智能识别消息中的物品实体，决定返回合成卡片还是普通文本

### 核心逻辑变更
- **对话级合成上下文**：合成树存储在conversation级别，而非message级别
- **智能物品识别**：后端通过NLP技术自动识别消息中的物品实体
- **响应类型决策**：根据是否识别到物品实体决定返回合成卡片或普通文本
- **前端渲染逻辑**：前端根据item_name从对话合成树中提取物品信息进行渲染

---

## 二、核心数据结构

### 2.1 合成树节点数据结构
```typescript
// 合成树节点数据（基于 docs/craftingTree.ts）
export interface CraftingTreeNodeData {
  itemId: string;                     // 物品唯一标识符
  itemName: string;                   // 物品显示名称
  itemIcon: string;                   // 物品图标URL
  itemDesc?: string;                  // 物品描述，可选
  recipe: (string | null)[][];        // 3x3合成配方数组
  url: string;                        // 物品介绍网页URL，点击九宫格底部图标可跳转
  children: CraftingTreeNodeData[];   // 递归子树（合成所需材料）
}
```

### 2.2 消息数据结构
```typescript
// 消息数据
export interface Message {
  id: string;
  role: 'assistant' | 'user';
  type: 'CRAFTING' | 'TEXT';          // crafting: 合成卡片消息；text: 普通文本消息
  content: string;                    // 文本内容
  item_name?: string;                 // 仅当 type==='crafting' 时存在，指向合成树中的物品名称
  created_at: string;                 // ISO 时间戳
}
```

### 2.3 对话级合成上下文
```typescript
// 对话级合成上下文（存储在conversation级别）
export interface ConversationCraftingContext {
  targetItem: string;                 // 合成目标物品名称（根节点）
  sharedTree: CraftingTreeNodeData;   // 完整合成树，对话创建后保持不变
  created_at: string;                 // 合成树创建时间
}
```

### 2.4 对话数据结构扩展
```typescript
// 对话数据（扩展现有结构）
export interface Conversation {
  id: number;
  title: string;
  type: 'NORMAL' | 'SYNTHETIC_NAVIGATION';  // 对话类型
  craftingContext?: ConversationCraftingContext; // 仅合成导航对话存在
  created_at: string;
  updated_at: string;
  messages: Message[];
}
```

### 2.5 请求体结构
```typescript
// 统一的消息请求体（普通对话和合成导航共用）
export interface MessageRequest {
  message: string;                    // 用户消息内容
}

// 新建对话请求体
export interface NewConversationRequest {
  message: string;                    // 首条消息内容
  type: 'NORMAL' | 'SYNTHETIC_NAVIGATION'; // 对话类型
}
```

---

## 三、API 接口详细说明

### 3.1 新建对话接口

#### 接口定义
```http
POST /api/conversations/new
Accept: text/event-stream
Content-Type: application/json
Authorization: Bearer <token>
```

#### 请求体
```typescript
{
  "message": "钻石剑",                    // 用户消息内容
  "type": "SYNTHETIC_NAVIGATION"          // 对话类型
}
```

#### SSE 事件流（合成导航模式）
1. **conversation_start**
   ```json
   {
     "conversationId": 123,
     "type": "SYNTHETIC_NAVIGATION",
     "title": "钻石剑..."
   }
   ```

2. **crafting_context**（仅首次创建合成导航对话时发送）
   ```json
   {
     "targetItem": "钻石剑",
     "sharedTree": {
       "itemId": "diamond_sword",
       "itemName": "钻石剑",
       "itemIcon": "...",
       "itemDesc": "钻石剑是Minecraft中最强的近战武器之一。",
       "url": "https://minecraft.fandom.com/zh/wiki/钻石剑",
       "recipe": [
         [null, "diamond", null],
         [null, "diamond", null],
         [null, "stick", null]
       ],
       "children": [
         {
           "itemId": "diamond",
           "itemName": "钻石",
           "itemIcon": "...",
           "itemDesc": "钻石，可用于合成多种高级物品。",
           "url": "https://minecraft.fandom.com/zh/wiki/钻石",
           "recipe": [
             [null, null, null],
             [null, "diamond_ore", null],
             [null, null, null]
           ],
           "children": [
             {
               "itemId": "diamond_ore",
               "itemName": "钻石矿石",
               "itemIcon": "...",
               "itemDesc": "Y坐标16及以下矿洞中，用铁镐或更高级矿镐采集获得。",
               "url": "https://minecraft.fandom.com/zh/wiki/钻石矿石",
               "recipe": [
                 [null, null, null],
                 [null, null, null],
                 [null, null, null]
               ],
               "children": []
             }
           ]
         },
         {
           "itemId": "stick",
           "itemName": "木棒",
           "itemIcon": "...",
           "itemDesc": "木棒，基础合成材料。",
           "url": "https://minecraft.fandom.com/zh/wiki/木棒",
           "recipe": [
             [null, null, null],
             [null, "planks", null],
             [null, "planks", null]
           ],
           "children": [
             {
               "itemId": "planks",
               "itemName": "木板",
               "itemIcon": "...",
               "itemDesc": "木板，由原木合成。",
               "url": "https://minecraft.fandom.com/zh/wiki/木板",
               "recipe": [
                 [null, null, null],
                 [null, "log", null],
                 [null, null, null]
               ],
               "children": [
                 {
                   "itemId": "log",
                   "itemName": "原木",
                   "itemIcon": "...",
                   "itemDesc": "原木可通过砍伐树木获得，是最基础的采集材料。",
                   "url": "https://minecraft.fandom.com/zh/wiki/原木",
                   "recipe": [
                     [null, null, null],
                     [null, null, null],
                     [null, null, null]
                   ],
                   "children": []
                 }
               ]
             }
           ]
         }
       ]
     }
   }
   ```

3. **message**（流式响应内容）
   ```json
   { "chunk": "钻石剑是一种强力的近战武器..." }
   ```

4. **message_complete**（消息完成，包含完整消息信息）
   ```json
   {
     "messageId": 456,
     "type": "crafting",
     "item_name": "钻石剑"
   }
   ```

5. **end**
   ```json
   {}
   ```

6. **error**（错误情况）
   ```json
   { "message": "错误信息" }
   ```

### 3.2 后续消息接口

#### 接口定义
```http
POST /api/conversations/{conversationId}/messages
Accept: text/event-stream
Content-Type: application/json
Authorization: Bearer <token>
```

#### 请求体
```typescript
{
  "message": "它的攻击力是多少？"        // 用户消息内容（统一格式，无需特殊处理）
}
```

#### SSE 事件流

**情况1：识别到物品实体**
1. **message**（流式响应内容）
   ```json
   { "chunk": "钻石剑的攻击力为7点..." }
   ```

2. **message_complete**（消息完成）
   ```json
   {
     "messageId": 789,
     "type": "crafting",
     "item_name": "钻石剑"
   }
   ```

3. **end**
   ```json
   {}
   ```

**情况2：未识别到物品实体**
1. **message**（流式响应内容）
   ```json
   { "chunk": "我可以帮您了解更多关于合成的信息..." }
   ```

2. **message_complete**（消息完成）
   ```json
   {
     "messageId": 790,
     "type": "text"
   }
   ```

3. **end**
   ```json
   {}
   ```

## 四、SSE 事件类型定义

### 4.1 事件接口定义
```typescript
// 对话开始事件
interface ConversationStartEvent {
  conversationId: number;
  type: 'NORMAL' | 'SYNTHETIC_NAVIGATION';
  title: string;
}

// 合成上下文事件（仅合成导航对话的首次创建时发送）
interface CraftingContextEvent {
  targetItem: string;
  sharedTree: CraftingTreeNodeData;
}

// 消息流片段事件
interface MessageChunkEvent {
  chunk: string;                        // 流式内容片段
}

// 消息完成事件
interface MessageCompleteEvent {
  messageId: number;
  type: 'crafting' | 'text';
  item_name?: string;                   // 仅当 type === 'crafting' 时存在
}

// 错误事件
interface ErrorEvent {
  message: string;                      // 错误描述
}
```

### 4.2 事件发送顺序

#### 新建合成导航对话
1. `conversation_start` → ConversationStartEvent
2. `crafting_context` → CraftingContextEvent（仅首次）
3. `message` → MessageChunkEvent（可多次）
4. `message_complete` → MessageCompleteEvent
5. `end` → {}
6. `error` → ErrorEvent（错误时）

#### 后续消息（合成导航对话）
1. `message` → MessageChunkEvent（可多次）
2. `message_complete` → MessageCompleteEvent
3. `end` → {}
4. `error` → ErrorEvent（错误时）

#### 普通对话（保持兼容）
1. `conversation_start` → ConversationStartEvent（仅新建时）
2. `message` → MessageChunkEvent（可多次）
3. `end` → {}
4. `error` → ErrorEvent（错误时）

## 五、前后端职责分工

### 5.1 前端职责
- **对话类型选择**：用户创建对话时选择对话类型
- **统一消息发送**：所有消息使用相同的请求格式，无需特殊处理
- **智能渲染决策**：根据 `message_complete` 事件中的 `type` 和 `item_name` 决定渲染方式
  - `type === 'crafting'`：从对话的 `craftingContext.sharedTree` 中查找 `item_name` 对应的物品信息，渲染合成卡片
  - `type === 'text'`：渲染普通文本消息
- **合成上下文管理**：在对话级别维护 `craftingContext`，供后续消息渲染使用

### 5.2 后端职责
- **物品实体识别**：通过NLP技术识别用户消息中的物品实体
- **合成树生成**：首次识别到物品时，生成完整的合成树（需要Neo4j支持）
- **响应类型决策**：根据物品识别结果决定返回 `crafting` 或 `text` 类型消息
- **上下文存储**：在数据库中存储对话级别的合成上下文

## 六、与普通模式接口对比

| 功能特性         | 普通对话模式                              | 合成导航模式                                         |
|------------------|-------------------------------------------|--------------------------------------------------|
| **对话创建**     | `type: "NORMAL"`                          | `type: "SYNTHETIC_NAVIGATION"`                   |
| **请求格式**     | `{ "message": "..." }`                    | `{ "message": "...", "type": "SYNTHETIC_NAVIGATION" }` |
| **首次消息事件** | conversation_start → message → end       | conversation_start → crafting_context → message → message_complete → end |
| **后续消息事件** | message → end                             | message → message_complete → end                 |
| **消息类型**     | 固定为 `text`                             | 动态决定：`crafting` 或 `text`                   |
| **上下文管理**   | 无特殊上下文                              | 对话级 `craftingContext`                         |
| **前端渲染**     | 统一文本渲染                              | 根据 `type` 和 `item_name` 智能渲染               |

## 七、物品实体识别逻辑

### 7.1 识别策略
1. **实体抽取**：使用NLP技术从用户消息中提取可能的物品名称
2. **物品验证**：验证提取的实体是否为有效的Minecraft物品
3. **上下文匹配**：优先匹配当前对话合成树中已存在的物品
4. **新物品处理**：如果识别到新物品且当前对话尚无合成树，则生成新的合成树

### 7.2 响应决策
```typescript
// 伪代码示例
function determineResponseType(userMessage: string, craftingContext?: ConversationCraftingContext) {
  const extractedItems = extractItemEntities(userMessage);

  if (extractedItems.length > 0) {
    const primaryItem = extractedItems[0]; // 选择主要物品
    return {
      type: 'crafting',
      item_name: primaryItem,
      needsNewTree: !craftingContext // 是否需要生成新合成树
    };
  } else {
    return {
      type: 'text'
    };
  }
}
```

## 八、兼容性说明

### 8.1 向后兼容
- **现有普通对话**：完全保持现有API格式和行为不变
- **前端适配**：通过 `type` 字段区分对话模式，无需大幅修改现有代码
- **数据库兼容**：新增字段均为可选，不影响现有数据

### 8.2 渐进式实现
1. **阶段1**：实现基础的对话类型区分和数据结构
2. **阶段2**：实现物品实体识别（可先用简单规则，后续优化为NLP）
3. **阶段3**：接入Neo4j实现真实的合成树生成
4. **阶段4**：优化物品识别准确率和响应性能

以上即为完整的合成导航模式API接口说明，涵盖了数据结构、接口定义、事件格式、前后端职责分工以及后端实现要点等所有关键信息。
