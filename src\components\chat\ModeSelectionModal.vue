<template>
  <div v-if="isVisible" class="mode-selection-modal" @click.self="closeModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>🚀 选择对话模式</h2>
        <button class="close-btn" @click="closeModal">
          <span class="material-icons">close</span>
        </button>
      </div>
      
      <div class="mode-cards-container">
        <!-- 普通对话模式 -->
        <div 
          class="mode-card normal-mode" 
          :class="{ 'selected': selectedMode === 'normal', 'selecting': selectingMode === 'normal' }"
          @click="selectMode('normal')"
          @keydown.enter="selectMode('normal')"
          tabindex="0"
        >
          <div class="mode-icon">💬</div>
          <div class="mode-title">普通对话</div>
          <div class="mode-description">
            <div class="feature-item">📝 自由聊天讨论</div>
            <div class="feature-item">🤔 任何MC相关问题</div>
            <div class="feature-item">💡 游戏技巧交流</div>
            <div class="feature-item">🏗️ 建筑设计咨询</div>
          </div>
          <div class="mode-action">
            <button class="select-btn">选择此模式</button>
          </div>
        </div>

        <!-- 合成导航模式 -->
        <div 
          class="mode-card crafting-mode" 
          :class="{ 'selected': selectedMode === 'crafting', 'selecting': selectingMode === 'crafting' }"
          @click="selectMode('crafting')"
          @keydown.enter="selectMode('crafting')"
          tabindex="0"
        >
          <div class="mode-icon">🔧</div>
          <div class="mode-title">合成导航</div>
          <div class="mode-description">
            <div class="feature-item">⚒️ 物品制作指导</div>
            <div class="feature-item">🌳 可视化合成树</div>
            <div class="feature-item">📊 材料依赖分析</div>
            <div class="feature-item">🔍 配方详细展示</div>
          </div>
          <div class="mode-action">
            <button class="select-btn">选择此模式</button>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <div class="keyboard-hint">
          💡 提示：按 <kbd>1</kbd> 选择普通对话，按 <kbd>2</kbd> 选择合成导航，按 <kbd>ESC</kbd> 取消
        </div>
        <button class="cancel-btn" @click="closeModal">取消</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';

interface Props {
  isVisible: boolean;
}

interface Emits {
  (e: 'close'): void;
  (e: 'selectMode', mode: 'normal' | 'crafting'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const selectedMode = ref<'normal' | 'crafting' | null>(null);
const selectingMode = ref<'normal' | 'crafting' | null>(null);

const selectMode = (mode: 'normal' | 'crafting') => {
  if (selectingMode.value) return; // 防止重复选择
  
  selectedMode.value = mode;
  selectingMode.value = mode;
  
  // 添加选择动画效果
  setTimeout(() => {
    emit('selectMode', mode);
    closeModal();
  }, 800);
};

const closeModal = () => {
  selectedMode.value = null;
  selectingMode.value = null;
  emit('close');
};

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (!props.isVisible) return;
  
  switch (event.key) {
    case 'Escape':
      closeModal();
      break;
    case '1':
      selectMode('normal');
      break;
    case '2':
      selectMode('crafting');
      break;
  }
};

// 监听键盘事件
onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});

// 当弹窗显示时重置状态
watch(() => props.isVisible, (visible) => {
  if (visible) {
    selectedMode.value = null;
    selectingMode.value = null;
  }
});
</script>

<style lang="scss" scoped>
.mode-selection-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(4px);
  }
}

.modal-content {
  background: rgba(20, 30, 20, 0.95);
  border: 3px solid #55ff55;
  border-radius: 12px;
  box-shadow: 0 0 30px rgba(85, 255, 85, 0.3);
  padding: 24px;
  max-width: 600px;
  width: 85%;
  max-height: 80vh;
  overflow-y: auto;
  animation: contentSlideIn 0.4s ease;
  font-family: 'Minecraft', monospace;
}

@keyframes contentSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(30px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  h2 {
    color: #55ff55;
    margin: 0;
    font-size: 24px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  }
  
  .close-btn {
    background: rgba(255, 85, 85, 0.2);
    border: 1px solid #ff5555;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff5555;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(255, 85, 85, 0.3);
      transform: scale(1.1);
    }
    
    .material-icons {
      font-size: 20px;
    }
  }
}

.mode-cards-container {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
}

.mode-card {
  flex: 1;
  background: rgba(30, 40, 30, 0.8);
  border: 3px solid #666;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: cardSlideIn 0.5s ease;
  
  &:hover {
    transform: translateY(-4px);
    border-color: #55ff55;
    box-shadow: 0 8px 25px rgba(85, 255, 85, 0.25);
  }
    &.crafting-mode:hover {
    border-color: #2196f3;
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.25);
  }
  
  &.selected {
    border-color: #55ff55;
    box-shadow: 0 0 20px rgba(85, 255, 85, 0.4);
  }
    &.crafting-mode.selected {
    border-color: #2196f3;
    box-shadow: 0 0 20px rgba(33, 150, 243, 0.4);
  }
  
  &.selecting {
    animation: cardSelected 0.8s ease;
  }
  
  &:focus {
    outline: none;
    border-color: #55ff55;
    box-shadow: 0 0 15px rgba(85, 255, 85, 0.3);
  }
    &.crafting-mode:focus {
    border-color: #2196f3;
    box-shadow: 0 0 15px rgba(33, 150, 243, 0.3);
  }
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes cardSelected {
  0% { transform: scale(1); }
  30% { transform: scale(1.05); }
  60% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

.mode-icon {
  font-size: 40px;
  text-align: center;
  margin-bottom: 12px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
}

.mode-title {
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16px;
  color: #ffffff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.mode-description {
  margin-bottom: 20px;
  
  .feature-item {
    color: #cccccc;
    margin-bottom: 8px;
    font-size: 14px;
    display: flex;
    align-items: center;
    padding: 4px 0;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.mode-action {
  text-align: center;
    .select-btn {
    background: linear-gradient(135deg, #55ff55 0%, #33dd33 100%);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    color: #000000;
    font-weight: bold;
    font-family: 'Minecraft', monospace;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
    box-shadow: 0 2px 8px rgba(85, 255, 85, 0.3);
    
    &:hover {
      background: linear-gradient(135deg, #77ff77 0%, #55ee55 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(85, 255, 85, 0.4);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

.crafting-mode .mode-action .select-btn {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
  
  &:hover {
    background: linear-gradient(135deg, #42a5f5 0%, #2196f3 100%);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
  }
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba(85, 255, 85, 0.3);
  padding-top: 16px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}

.keyboard-hint {
  color: #aaaaaa;
  font-size: 12px;
  
  kbd {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 11px;
    color: #ffffff;
    margin: 0 2px;
  }
}

.cancel-btn {
  background: rgba(255, 85, 85, 0.2);
  border: 1px solid #ff5555;
  border-radius: 6px;
  padding: 8px 16px;
  color: #ff5555;
  font-family: 'Minecraft', monospace;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 13px;
  
  &:hover {
    background: rgba(255, 85, 85, 0.3);
    transform: translateY(-1px);
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .modal-content {
    background: rgba(15, 25, 15, 0.98);
  }
  
  .mode-card {
    background: rgba(25, 35, 25, 0.9);
  }
}
</style>
