import { AxiosResponse } from 'axios';
import httpService from './httpService';

export interface KnowledgeBaseItem {
  title: string;
  content: string;
}

export interface KnowledgeBaseResponse {
  content: KnowledgeBaseItem[];
  page_count: number;
}

export interface UploadKnowledgeBaseResponse {
  success: boolean;
}

/**
 * 分页获取某个知识库的内容
 * @param type 知识库种类
 * @param start_index 起始下标
 * @param page_size 每页大小
 * @returns AxiosResponse<KnowledgeBaseResponse>
 */
export const getKnowledgeBase = async (
  type: 'WIKI' | 'POST' | 'BBS' | 'MODE' | 'ITEM' | 'NEW',
  start_index: number,
  page_size: number
): Promise<AxiosResponse<KnowledgeBaseResponse>> => {
  return await httpService.post<KnowledgeBaseResponse>('/admin/getKnowledgeBase', {
    type,
    start_index,
    page_size
  });
};

/**
 * 上传知识库内容
 * @param dataArr 包含file（File）的对象数组
 * @returns AxiosResponse<{success: boolean}>
 */
export const uploadKnowledgeBase = async (
  dataArr: { file: File }[]
): Promise<AxiosResponse<UploadKnowledgeBaseResponse>> => {
  const formData = new FormData();
  dataArr.forEach((item, idx) => {
    // 文件
    formData.append(`file[${idx}]`, item.file);
  });
  return await httpService.post<UploadKnowledgeBaseResponse>(
    '/admin/uploadKnowledgeBase',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  );
};
