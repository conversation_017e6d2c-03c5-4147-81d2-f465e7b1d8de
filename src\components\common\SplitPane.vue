<template>
  <div class="split-pane" :class="direction">
    <div class="split-pane-pane split-pane-left" :style="leftStyle">
      <slot name="left" />
    </div>
    <div class="split-pane-divider" @mousedown="onMouseDown" @touchstart="onTouchStart"></div>
    <div class="split-pane-pane split-pane-right" :style="rightStyle">
      <slot name="right" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';

const props = defineProps({
  minLeft: { type: Number, default: 240 },
  minRight: { type: Number, default: 320 },
  defaultLeft: { type: Number, default: 400 },
  direction: { type: String, default: 'horizontal' }, // 未来可支持vertical
  persistKey: { type: String, default: '' }, // 用于持久化分屏比例
});

const leftWidth = ref(props.defaultLeft);
const dragging = ref(false);
const startX = ref(0);
const startWidth = ref(0);

const leftStyle = computed(() => ({
  width: leftWidth.value + 'px',
  minWidth: props.minLeft + 'px',
}));
const rightStyle = computed(() => ({
  minWidth: props.minRight + 'px',
}));

function onMouseDown(e: MouseEvent) {
  dragging.value = true;
  startX.value = e.clientX;
  startWidth.value = leftWidth.value;
  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
}
function onTouchStart(e: TouchEvent) {
  if (e.touches.length !== 1) return;
  dragging.value = true;
  startX.value = e.touches[0].clientX;
  startWidth.value = leftWidth.value;
  document.addEventListener('touchmove', onTouchMove);
  document.addEventListener('touchend', onTouchEnd);
}
function onMouseMove(e: MouseEvent) {
  if (!dragging.value) return;
  const delta = e.clientX - startX.value;
  let newWidth = startWidth.value + delta;
  newWidth = Math.max(props.minLeft, newWidth);
  leftWidth.value = newWidth;
}
function onTouchMove(e: TouchEvent) {
  if (!dragging.value || e.touches.length !== 1) return;
  const delta = e.touches[0].clientX - startX.value;
  let newWidth = startWidth.value + delta;
  newWidth = Math.max(props.minLeft, newWidth);
  leftWidth.value = newWidth;
}
function onMouseUp() {
  dragging.value = false;
  document.removeEventListener('mousemove', onMouseMove);
  document.removeEventListener('mouseup', onMouseUp);
  persistWidth();
}
function onTouchEnd() {
  dragging.value = false;
  document.removeEventListener('touchmove', onTouchMove);
  document.removeEventListener('touchend', onTouchEnd);
  persistWidth();
}
function persistWidth() {
  if (props.persistKey) {
    localStorage.setItem('splitpane-' + props.persistKey, String(leftWidth.value));
  }
}
function restoreWidth() {
  if (props.persistKey) {
    const v = localStorage.getItem('splitpane-' + props.persistKey);
    if (v && !isNaN(Number(v))) {
      leftWidth.value = Number(v);
    }
  }
}
onMounted(() => {
  restoreWidth();
});
onBeforeUnmount(() => {
  if (dragging.value) {
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
    document.removeEventListener('touchmove', onTouchMove);
    document.removeEventListener('touchend', onTouchEnd);
  }
});
</script>

<style scoped lang="scss">
.split-pane {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  &.horizontal {
    flex-direction: row;
  }
}
.split-pane-pane {
  height: 100%;
  overflow: auto;
  background: transparent;
}
.split-pane-pane.split-pane-right {
  flex: 1 1 0%;
  width: auto !important;
  min-width: 0;
  height: 100%;
  overflow: auto;
  background: transparent;
}
.split-pane-divider {
  width: 7px;
  background: linear-gradient(90deg, #23272a 0%, #55ff55 100%);
  cursor: col-resize;
  z-index: 10;
  position: relative;
  transition: background 0.2s;
  border-radius: 4px;
  margin: 0 2px;
}
.split-pane-divider:hover {
  background: linear-gradient(90deg, #55ff55 0%, #23272a 100%);
}
</style>
