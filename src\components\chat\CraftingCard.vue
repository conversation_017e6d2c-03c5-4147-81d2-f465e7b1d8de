<template>
  <div>
    <!-- 非分屏：嵌入式卡片，可点击，顶部标题+icon+内容滚动 -->
    <div v-if="!isSplitPane" class="canvas-crafting-card" @click="handleClick">
      <span class="split-icon" @click.stop="handleClick" title="在右侧打开">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <!-- 右上箭头（↗） -->
          <polyline points="10,2 14,2 14,6" stroke="#55ff55" stroke-width="1.3" fill="none" stroke-linecap="round"/>
          <polyline points="10,6 14,2" stroke="#55ff55" stroke-width="1.3" fill="none" stroke-linecap="round"/>
          <!-- 左下箭头（↙） -->
          <polyline points="6,14 2,14 2,10" stroke="#55ff55" stroke-width="1.3" fill="none" stroke-linecap="round"/>
          <polyline points="6,10 2,14" stroke="#55ff55" stroke-width="1.3" fill="none" stroke-linecap="round"/>
        </svg>
      </span>
      <div class="card-header with-split-icon">
        <span class="card-quote-icon">&#x275D;</span>
        <span class="card-title">{{ craftingData?.itemName || '合成导航' }}</span>
        <span class="card-icon">
          <img :src="craftingData?.itemIcon || '/assets/creeper.webp'" alt="icon" />
        </span>
      </div>
      <div class="card-content-scroll">
        <!-- 默认插槽：若未传入内容，则渲染合成配方卡并转发 select-item -->
        <slot>
          <CraftingRecipeCard
            v-if="craftingData"
            :item="craftingData"
            @select-item="itemName => { console.log('[CraftingCard] select-item', itemName); emit('select-item', itemName) }"
          />
        </slot>
      </div>
    </div>
    <!-- 分屏：退化为标签，不可点击，紧凑美观 -->
    <div v-else class="canvas-crafting-tag">
      <span class="tag-quote-icon">&#x275D;</span>      <span class="tag-title">{{ craftingData?.itemName || '合成导航' }}</span>
      <span class="tag-icon">
        <img :src="craftingData?.itemIcon || '/assets/creeper.webp'" alt="icon" />
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import type { PropType } from 'vue';
import type { CraftingTreeNodeData } from '@/types';
import CraftingRecipeCard from './CraftingRecipeCard.vue';
const props = defineProps<{
  craftingData?: CraftingTreeNodeData;
  description?: string;
  isSplitPane?: boolean;
}>();
const emit = defineEmits(['expand-split', 'select-item']); // Add 'select-item' to emits

function handleClick() {
  if (!props.isSplitPane) {
    console.log('[CraftingCard] handleClick with craftingData:', props.craftingData);
    // 确保传递合成树数据
    emit('expand-split', {
      type: 'crafting',
      craftingData: props.craftingData
    });
  }
}
</script>

<style scoped lang="scss">
.canvas-crafting-card {
  background: #fff;
  border: 1.5px solid #55ff55;
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(85,255,85,0.10);
  margin: 4px 0 8px 0; // 向上靠近
  cursor: pointer;
  transition: box-shadow 0.18s, border 0.18s;
  position: relative;
  width: 320px;
  max-width: 100%;
  min-width: 320px;
}
.card-header.with-split-icon {
  position: relative;
  padding-right: 38px; // 给右上角icon留空间
}
.split-icon {
  position: absolute;
  top: 14px; // 向下移动，与标题垂直居中
  right: 12px;
  width: 18px;
  height: 18px;
  border-radius: 4px;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 3;
  transition: background 0.18s, box-shadow 0.18s;
  &:hover {
    background: #eaffea;
    box-shadow: 0 2px 8px #55ff5533;
    svg polyline {
      stroke: #22bb55;
    }
  }
  svg {
    display: block;
    width: 16px;
    height: 16px;
  }
}
.card-header {
  display: flex;
  align-items: center;
  padding: 10px 18px 6px 14px;
  border-bottom: 1px solid #e0e0e0;
  background: transparent;
}
.card-quote-icon {
  font-size: 18px;
  color: #55ff55;
  margin-right: 8px;
}
.card-title {
  font-size: 15px;
  color: #333;
  font-weight: 600;
  flex: 1;
  font-family: 'Minecraft', sans-serif;
}
.card-icon {
  width: 28px;
  height: 28px;
  margin-left: 8px;
  margin-top: -5px; // 向上微调，与标题基线对齐
  display: flex;
  align-items: center;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 6px;
    background: #e8ffe8;
    border: 1px solid #b2ffb2;
  }
}
.card-content-scroll {
  max-height: 180px;
  overflow-y: auto;
  padding: 14px 18px 16px 18px;
  font-size: 14px;
  color: #222;
  font-family: 'Minecraft', sans-serif;
  background: #f8fff8;
  border-radius: 0 0 14px 14px;
}

// 分屏下标签样式
.canvas-crafting-tag {
  display: flex;
  align-items: center;
  background: #f0f8f0;
  border: 1.5px solid #b2ffb2;
  border-radius: 999px;
  padding: 6px 18px 6px 12px;
  margin: 4px 0 8px 0; // 向上靠近
  min-width: 120px;
  max-width: 340px;
  width: fit-content;
  font-family: 'Minecraft', sans-serif;
  font-size: 14px;
  color: #388838;
  box-shadow: 0 1px 6px 0 rgba(85,255,85,0.08);
  user-select: none;
  cursor: default;
}
.tag-quote-icon {
  font-size: 15px;
  color: #55ff55;
  margin-right: 7px;
}
.tag-title {
  font-size: 14px;
  font-weight: 600;
  margin-right: 8px;
}
.tag-icon {
  width: 22px;
  height: 22px;
  margin-top: -3px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 5px;
    background: #e8ffe8;
    border: 1px solid #b2ffb2;
  }
}
.tag-badge {
  background: #55ff55;
  color: #fff;
  font-size: 12px;
  border-radius: 8px;
  padding: 2px 10px;
  margin-left: 6px;
  font-weight: bold;
  letter-spacing: 1px;
}
</style>
