<template>
  <div v-if="isVisible" class="upload-overlay" @click.self="close">
    <div class="upload-modal">
      <div class="upload-header">上传知识库内容</div>
      <div class="upload-list">
        <label class="upload-file-btn">
          <span class="material-icons">upload_file</span>
          选择文件
          <input type="file" multiple @change="handleFileChange" style="display:none" />
        </label>
        <div v-if="files.length === 0" class="upload-empty">请先选择要上传的文件</div>
        <div v-for="(item, idx) in files" :key="item.id" class="upload-item">
          <span class="file-name">{{ item.file.name }}</span>
          <button class="remove-btn" @click="removeFile(idx)" title="移除文件">
            <span class="material-icons">close</span>
          </button>
        </div>
      </div>
      <div class="upload-actions">
        <button class="cancel-btn" @click="close">取消</button>
        <button class="upload-btn" :disabled="files.length === 0 || uploading" @click="startUpload">
          {{ uploading ? '上传中...' : '开始上传' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';

const props = defineProps<{ isVisible: boolean }>();
const emit = defineEmits(['close', 'upload']);

interface UploadItem {
  id: number;
  file: File;
}

const files = reactive<UploadItem[]>([]);
const uploading = ref(false);

function handleFileChange(e: Event) {
  const input = e.target as HTMLInputElement;
  if (input.files) {
    for (const file of Array.from(input.files)) {
      files.push({ id: Date.now() + Math.random(), file });
    }
    input.value = '';
  }
}
function removeFile(idx: number) {
  files.splice(idx, 1);
}
function close() {
  if (!uploading.value) emit('close');
}
function startUpload() {
  if (files.length === 0) return;
  uploading.value = true;
  try {
    // 组装数据
    const dataArr = files.map(item => ({
      file: item.file
    }));
    console.log('上传数据', dataArr);
    // 触发父组件上传逻辑
    emit('upload', dataArr);
    files.splice(0, files.length);
    close();
  } finally {
    uploading.value = false;
  }
}
</script>

<style scoped lang="scss">
.upload-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  z-index: 2000;
  background: rgba(30, 40, 30, 0.45);
  backdrop-filter: blur(6px);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.2s;
}
.upload-modal {
  background: #181c18;
  border: 2px solid #5e5e5e;
  border-radius: 12px;
  min-width: 480px;
  max-width: 98vw;
  width: 600px;
  box-shadow: 0 4px 32px #000a;
  padding: 0 0 28px 0;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.25s;
}
.upload-header {
  font-family: 'Minecraft', monospace;
  font-size: 1.3rem;
  color: #55ff55;
  text-shadow: 1px 1px 6px #000, 0 0 6px #55ff5588;
  padding: 32px 0 20px 0;
  text-align: center;
  border-bottom: 2px solid #333;
  letter-spacing: 2px;
  user-select: none;
}
.upload-list {
  padding: 28px 38px 0 38px;
  flex: 1;
  min-height: 120px;
}
.upload-file-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: #23272a;
  color: #55ff55;
  border: 1.5px solid #55ff55;
  border-radius: 6px;
  font-family: 'Minecraft', monospace;
  font-size: 1rem;
  padding: 7px 18px;
  cursor: pointer;
  margin-bottom: 18px;
  transition: background 0.2s, color 0.2s;
  user-select: none;
}
.upload-file-btn:hover {
  background: #55ff55;
  color: #23272a;
}
.upload-empty {
  color: #aaa;
  text-align: center;
  margin: 24px 0 0 0;
  font-size: 1rem;
}
.upload-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  background: #23272a;
  border-radius: 6px;
  padding: 8px 12px;
}
.file-name {
  color: #fff;
  font-size: 1rem;
  font-family: 'Minecraft', monospace;
  flex: 1;
  user-select: text;
}
.remove-btn {
  background: none;
  border: none;
  color: #ff5555;
  font-size: 1.2rem;
  cursor: pointer;
  border-radius: 4px;
  padding: 4px 6px;
  transition: background 0.2s, color 0.2s;
}
.remove-btn:hover {
  background: #ff5555;
  color: #fff;
}
.upload-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 0 38px;
  margin-top: 28px;
}
.cancel-btn {
  background: #23272a;
  color: #aaa;
  border: 1.5px solid #5e5e5e;
  border-radius: 6px;
  font-family: 'Minecraft', monospace;
  font-size: 1rem;
  padding: 7px 22px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.cancel-btn:hover {
  background: #444;
  color: #fff;
}
.upload-btn {
  background: #55ff55;
  color: #23272a;
  border: 1.5px solid #55ff55;
  border-radius: 6px;
  font-family: 'Minecraft', monospace;
  font-size: 1rem;
  padding: 7px 22px;
  cursor: pointer;
  font-weight: bold;
  transition: background 0.2s, color 0.2s;
}
.upload-btn:disabled {
  background: #aaa;
  color: #fff;
  border-color: #aaa;
  cursor: not-allowed;
}
.upload-btn:not(:disabled):hover {
  background: #7dff7d;
  color: #23272a;
}
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
@keyframes slideUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}
</style>
