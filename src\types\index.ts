// 合成树节点数据结构
export interface CraftingTreeNodeData {
  itemId: string;                     // 物品唯一标识符
  itemName: string;                   // 物品显示名称
  itemIcon: string;                   // 物品图标URL
  itemDesc?: string;                  // 物品描述，可选
  recipe: (string | null)[][];        // 3x3合成配方数组
  url: string;                        // 物品介绍网页URL，点击九宫格底部图标可跳转
  children: CraftingTreeNodeData[];   // 递归子树（合成所需材料）
}

// 消息数据结构
export interface Message {
  id: string;                         // 消息唯一ID
  role: 'assistant' | 'user';         // 角色
  type: 'CRAFTING' | 'TEXT';          // 消息类型（全部大写，严格对齐API）
  content: string;                    // 文本内容
  item_name?: string;                 // 仅当 type==='CRAFTING' 时存在，指向合成树中的物品名称
  created_at: string;                 // ISO 时间戳
  // 前端运行时扩展字段（非后端API字段）
  isLoading?: boolean;                // 是否为流式加载中
  isError?: boolean;                  // 是否为错误消息
  craftingData?: any;                 // 合成卡片数据（仅CRAFTING类型时）
}

// 对话级合成上下文
export interface ConversationCraftingContext {
  targetItem: string;                 // 合成目标物品名称（根节点）
  sharedTree: CraftingTreeNodeData;   // 完整合成树，对话创建后保持不变
  created_at: string;                 // 合成树创建时间
}

// 对话数据结构
export interface Conversation {
  id: number;
  title: string;
  type: 'NORMAL' | 'SYNTHETIC_NAVIGATION';  // 对话类型
  craftingContext?: ConversationCraftingContext; // 仅合成导航对话存在
  created_at: string;
  updated_at: string;
  messages: Message[];
}

// 其余用户相关类型保持不变
export interface UserState {
  token: string | null;
  user: {
    user_id?: number;
    username?: string;
    email?: string;
    created_at?: string;
  } | null;
  isAuthenticated: boolean;
  error: string | null;
  loading: boolean;
  conversations: Conversation[];
  currentConversation: Conversation | null;
  loadingConversations: boolean;
  loadingMessages: boolean;
  feedbackSubmitting: boolean;
  currentAbortController: AbortController | null;
  showModeSelectionModal: boolean;
}

export interface UserData {
  user_id?: number;
  username?: string;
  email?: string;
  created_at?: string;
  role?: 'ADMIN' | 'USER';
}
