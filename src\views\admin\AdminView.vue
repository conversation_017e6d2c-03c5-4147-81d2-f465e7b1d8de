<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import AdminHeader from '@/components/admin/AdminHeader.vue';
import AdminSidebar from '@/components/admin/AdminSidebar.vue';
import JsonPage from '@/components/admin/JsonPage.vue';
import { getKnowledgeBase, uploadKnowledgeBase } from '@/services/admin';

// 当前选中的知识库类型
const currentType = ref<'WIKI' | 'POST' | 'BBS' | 'MODE' | 'ITEM' | 'NEW'>('NEW');
const content = ref<any[]>([]);
const pageCount = ref(0);
const currentPage = ref(1);

async function fetchKnowledgeBase() {
  const res = await getKnowledgeBase(currentType.value, (currentPage.value - 1) * 5, 5);
  console.log(res);
  content.value = res.data.content;
  pageCount.value = res.data.page_count;
}

onMounted(fetchKnowledgeBase);
watch([currentType, currentPage], fetchKnowledgeBase);

async function handleUpload(dataArr: { file: File, metadata: string[] }[]) {
  try {
    const res = await uploadKnowledgeBase(dataArr);
    if (res.data.success) {
      alert('上传成功！');
      fetchKnowledgeBase(); // 上传后刷新
    } else {
      alert('上传失败');
    }
  } catch (e) {
    alert('上传出错');
  }
}
</script>

<template>
  <div class="admin-view-container">
    <AdminHeader />
    <div class="admin-main">
      <AdminSidebar :current-type="currentType" @update:type="currentType = $event" @upload="handleUpload"/>
      <div class="admin-content">
        <JsonPage :content="content" v-model:current-page="currentPage" :page-count="pageCount" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.admin-view-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f7fa;
}
.admin-main {
  display: flex;
  flex: 1;
  min-height: 0;
}
.admin-content {
  flex: 1;
  background: #f5f7fa;
  padding: 32px;
  min-height: 0;
  overflow: auto;
}
</style>